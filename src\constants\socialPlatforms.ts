import {
  Instagram,
  Twitter,
  Facebook,
  Linkedin,
  Youtube,
  Github,
  Music,
  MessageCircle,
  Send,
  Camera,
  Hash,
  Video,
  AtSign,
  Globe,
  Mail,
  Phone,
} from 'lucide-react';

export const socialPlatforms = {
  Instagram: { icon: Instagram, baseUrl: "https://instagram.com/" },
  Twitter:   { icon: Twitter,   baseUrl: "https://twitter.com/" },
  Facebook:  { icon: Facebook,  baseUrl: "https://facebook.com/" },
  Linkedin:  { icon: Linkedin,  baseUrl: "https://linkedin.com/" },
  Youtube:   { icon: Youtube,   baseUrl: "https://youtube.com/" },
  Github:    { icon: Github,    baseUrl: "https://github.com/" },
  TikTok:    { icon: Music,     baseUrl: "https://tiktok.com/@" },
  WhatsApp:  { icon: MessageCircle, baseUrl: "https://wa.me/" },
  Telegram:  { icon: Send,      baseUrl: "https://t.me/" },
  Discord:   { icon: MessageCircle, baseUrl: "https://discord.gg/" },
  Pinterest: { icon: Camera,    baseUrl: "https://pinterest.com/" },
  Reddit:    { icon: Hash,      baseUrl: "https://reddit.com/u/" },
  Snapchat:  { icon: Camera,    baseUrl: "https://snapchat.com/add/" },
  Twitch:    { icon: Video,     baseUrl: "https://twitch.tv/" },
  Threads:   { icon: AtSign,    baseUrl: "https://threads.net/@" },
  Website:   { icon: Globe,     baseUrl: "" },
  Email:     { icon: Mail,      baseUrl: "mailto:" },
  Phone:     { icon: Phone,     baseUrl: "tel:" },
};
