export interface LinkItem {
  id: string;
  title: string;
  url: string;
  icon: string;
  enabled: boolean;
}

export interface SocialMediaItem {
  id: string;
  platform: string;
  url: string;
  icon: string;
  enabled: boolean;
}

export interface Styles {
  theme: string;
  colors: {
    background: string;
    text: string;
    primary: string;
    primaryHover: string;
    cardBackground: string;
    border: string;
  };
  typography: {
    fontFamily: string;
    fontSize: string;
    headingSize: string;
  };
  layout: {
    borderRadius: string;
    spacing: string;
    maxWidth: string;
  };
  background: {
    type: string;
    value: string;
    image: string;
  };
}

export interface SEO {
  title: string;
  description: string;
  keywords: string;
  favicon: string;
  shareImage: string;
}

export interface Pages {
  privacy: {
    title: string;
    content: string;
  };
  terms: {
    title: string;
    content: string;
  };
}

export interface LinkData {
  userInfo: {
    name: string;
    bio: string;
    avatar: string;
  };
  links: LinkItem[];
  socialMedia: SocialMediaItem[];
  styles: Styles;
  seo: SEO;
  pages: Pages;
}

export interface Theme {
  name: string;
  value: string;
  colors: {
    background: string;
    text: string;
    primary: string;
    primaryHover: string;
    cardBackground: string;
    border: string;
  };
}
