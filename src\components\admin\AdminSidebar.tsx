import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Eye, EyeOff, Upload, Download, Save } from "lucide-react";

interface AdminSidebarProps {
  onShowPreview: () => void;
  onExportData: () => void;
  onImportData: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSaveData: () => void;
  isPreviewVisible: boolean;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({
  onShowPreview,
  onExportData,
  onImportData,
  onSaveData,
  isPreviewVisible,
}) => {
  return (
    <div className="border-b bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button asChild size="lg" className="min-w-fit" variant="outline">
              <Link href="/" className="flex items-center gap-2">
                <ArrowLeft size={16} />
                Back
              </Link>
            </Button>
          </div>
          <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
            <Button
              onClick={onShowPreview}
              variant="outline"
              size="lg"
              className="flex-1 sm:flex-none min-w-fit"
            >
              {isPreviewVisible ? (
                <EyeOff size={16} className="mr-2" />
              ) : (
                <Eye size={16} className="mr-2" />
              )}
              {isPreviewVisible ? "Hide" : "Show"} Preview
            </Button>
            <Button
              onClick={onExportData}
              variant="outline"
              size="lg"
              className="flex-1 sm:flex-none min-w-fit"
            >
              <Download size={16} className="mr-2" />
              Export
            </Button>
            <label className="cursor-pointer flex-1 sm:flex-none">
              <Button
                variant="outline"
                size="lg"
                asChild
                className="w-full min-w-fit"
              >
                <span className="flex items-center justify-center">
                  <Upload size={16} className="mr-2" />
                  Import
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={onImportData}
                className="hidden"
              />
            </label>
            <Button
              onClick={onSaveData}
              size="lg"
              className="flex-1 sm:flex-none min-w-fit bg-primary hover:bg-blue-700 text-white"
            >
              <Save size={16} className="mr-2" />
              Save & Download
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSidebar;
