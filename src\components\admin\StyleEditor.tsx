import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { themes, backgroundOptions } from "@/constants/admin";
import { Styles } from "@/types/admin";
import { Button } from "@/components/ui/button";

interface StyleEditorProps {
  styles: Styles;
  onApplyTheme: (theme: string) => void;
  onUpdateStyle: (
    category: keyof Styles["colors"] | "background",
    value: string,
    type?: "color" | "class" | "image"
  ) => void;
}

const StyleEditor: React.FC<StyleEditorProps> = ({
  styles,
  onApplyTheme,
  onUpdateStyle,
}) => {
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        onUpdateStyle("background", reader.result as string, "image");
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Themes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {themes.map((theme) => (
              <button
                key={theme.value}
                onClick={() => onApplyTheme(theme.value)}
                className={`p-4 border rounded-lg text-left transition-all hover:scale-105 ${
                  styles.theme === theme.value
                    ? "ring-2 ring-blue-500 border-blue-500"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                aria-label={`Select ${theme.name} theme`}
              >
                <div className="font-medium">{theme.name}</div>
                <div className="flex gap-1 mt-2">
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: theme.colors.background }}
                  />
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: theme.colors.primary }}
                  />
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: theme.colors.text }}
                  />
                </div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Background</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm">Stylized Backgrounds</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-2">
              {backgroundOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() =>
                    onUpdateStyle("background", option.value, "class")
                  }
                  className={`p-4 border rounded-lg text-left transition-all hover:scale-105 ${
                    styles.background.value === option.value
                      ? "ring-2 ring-blue-500 border-blue-500"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  aria-label={`Select ${option.name} background`}
                >
                  <div className="font-medium">{option.name}</div>
                </button>
              ))}
            </div>
          </div>
          <div>
            <Label className="text-sm">Custom Image</Label>
            <div className="flex items-center gap-4 mt-2">
              <Input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="flex-1"
              />
              {styles.background.type === "image" &&
                styles.background.image && (
                  <img
                    src={styles.background.image}
                    alt="Background Preview"
                    className="w-16 h-16 object-cover rounded-lg border"
                  />
                )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Custom Colors</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Object.entries(styles.colors).map(([key, value]) => (
              <div key={key}>
                <Label className="text-sm capitalize">
                  {key.replace(/([A-Z])/g, " $1").trim()}
                </Label>
                <div className="flex gap-2 mt-1">
                  <input
                    type="color"
                    value={value}
                    onChange={(e) =>
                      onUpdateStyle(
                        key as keyof Styles["colors"],
                        e.target.value
                      )
                    }
                    className="w-10 h-10 border rounded cursor-pointer"
                  />
                  <Input
                    value={value}
                    onChange={(e) =>
                      onUpdateStyle(
                        key as keyof Styles["colors"],
                        e.target.value
                      )
                    }
                    className="flex-1"
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StyleEditor;
