import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import LayoutSettings from "./LayoutSettings";
import LayoutVisualEditor from "./LayoutVisualEditor";
import LayoutQuickPanel from "./LayoutQuickPanel";
import { Styles } from "@/types/admin";

interface LayoutSettingsWrapperProps {
  layout: Styles["layout"];
  onUpdateLayout: (field: keyof Styles["layout"], value: string) => void;
}

export const LayoutSettingsWrapper: React.FC<LayoutSettingsWrapperProps> = ({
  layout,
  onUpdateLayout,
}) => {
  const [activeTab, setActiveTab] = useState<"simple" | "advanced" | "visual">(
    "simple"
  );

  return (
    <div className="space-y-6">
      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as any)}
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="simple">Simples</TabsTrigger>
          <TabsTrigger value="advanced">Avançado</TabsTrigger>
          <TabsTrigger value="visual">Visual</TabsTrigger>
        </TabsList>

        <TabsContent value="simple" className="space-y-4">
          <LayoutSettings layout={layout} onUpdateLayout={onUpdateLayout} />
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="grid gap-4">
            <LayoutSettings layout={layout} onUpdateLayout={onUpdateLayout} />
            <LayoutVisualEditor
              layout={layout}
              onUpdateLayout={onUpdateLayout}
            />
          </div>
        </TabsContent>

        <TabsContent value="visual" className="space-y-4">
          <LayoutVisualEditor layout={layout} onUpdateLayout={onUpdateLayout} />
        </TabsContent>
      </Tabs>

      {/* Quick Panel - always visible */}
      <LayoutQuickPanel layout={layout} onUpdateLayout={onUpdateLayout} />
    </div>
  );
};

export default LayoutSettingsWrapper;
