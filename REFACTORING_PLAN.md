# REFACTORING_PLAN

## Inventory

### Components
- [ ] `src/app/layout.tsx`
- [ ] `src/components/admin/ColorPicker.tsx`
- [ ] `src/components/admin/EnhancedPreview.tsx`
- [ ] `src/components/admin/TemplateSelector.tsx`
- [ ] `src/components/link-page/LinkPageRenderer.tsx`
- [ ] `src/components/ui/toaster.tsx`

### Pages
- [ ] `src/app/admin/page.tsx`
- [ ] `src/app/page.tsx`
- [ ] `src/app/privacy/page.tsx`
- [ ] `src/app/terms/page.tsx`

### Hooks

### Utilities
- [ ] `src/lib/utils.ts`

### Other Files
- [ ] `src/app/api/health/route.ts`
- [ ] `src/app/api/link-data/route.ts`
- [ ] `src/components/ui/accordion.tsx`
- [ ] `src/components/ui/alert-dialog.tsx`
- [ ] `src/components/ui/alert.tsx`
- [ ] `src/components/ui/aspect-ratio.tsx`
- [ ] `src/components/ui/avatar.tsx`
- [ ] `src/components/ui/badge.tsx`
- [ ] `src/components/ui/breadcrumb.tsx`
- [ ] `src/components/ui/button.tsx`
- [ ] `src/components/ui/calendar.tsx`
- [ ] `src/components/ui/card.tsx`
- [ ] `src/components/ui/carousel.tsx`
- [ ] `src/components/ui/chart.tsx`
- [ ] `src/components/ui/checkbox.tsx`
- [ ] `src/components/ui/collapsible.tsx`
- [ ] `src/components/ui/command.tsx`
- [ ] `src/components/ui/context-menu.tsx`
- [ ] `src/components/ui/dialog.tsx`
- [ ] `src/components/ui/drawer.tsx`
- [ ] `src/components/ui/dropdown-menu.tsx`
- [ ] `src/components/ui/form.tsx`
- [ ] `src/components/ui/hover-card.tsx`
- [ ] `src/components/ui/input-otp.tsx`
- [ ] `src/components/ui/input.tsx`
- [ ] `src/components/ui/label.tsx`
- [ ] `src/components/ui/menubar.tsx`
- [ ] `src/components/ui/navigation-menu.tsx`
- [ ] `src/components/ui/pagination.tsx`
- [ ] `src/components/ui/popover.tsx`
- [ ] `src/components/ui/progress.tsx`
- [ ] `src/components/ui/radio-group.tsx`
- [ ] `src/components/ui/resizable.tsx`
- [ ] `src/components/ui/scroll-area.tsx`
- [ ] `src/components/ui/select.tsx`
- [ ] `src/components/ui/separator.tsx`
- [ ] `src/components/ui/sheet.tsx`
- [ ] `src/components/ui/sidebar.tsx`
- [ ] `src/components/ui/skeleton.tsx`
- [ ] `src/components/ui/slider.tsx`
- [ ] `src/components/ui/sonner.tsx`
- [ ] `src/components/ui/switch.tsx`
- [ ] `src/components/ui/table.tsx`
- [ ] `src/components/ui/tabs.tsx`
- [ ] `src/components/ui/textarea.tsx`
- [ ] `src/components/ui/toast.tsx`
- [ ] `src/components/ui/toggle-group.tsx`
- [ ] `src/components/ui/toggle.tsx`
- [ ] `src/components/ui/tooltip.tsx`
## Summary
- Total Components: 6
- Total Pages: 4
- Total Hooks: 0
- Total Utilities: 1
- Other Files: 49
- **Total Files**: 60

## Opportunities

### Responsibilities
1. **UI Components**: 
   - **ColorPicker, EnhancedPreview, TemplateSelector, LinkPageRenderer**: Handle user interactions, rendering components based on user input or state changes.
   - **UI components (`button.tsx`, `card.tsx`, etc.)**: Provide reusable UI elements with customization options.
  
2. **Pages**: 
   - **Admin Page**: Manages links and social media items; interacts with APIs to fetch and save data.
   - **Home, Privacy, Terms Pages**: Render content based on server-fetched data.

3. **Data & Business Logic**:
   - **API routes**: Handle data persistence, ensuring security with directory traversal prevention.

### Flags
- **Duplication**:
  - Similar logic in `EnhancedPreview` and `LinkPageRenderer` for rendering user profiles and links.
  
- **Large Files (>200 LOC)**:
  - **Admin Page**: Significant lines of rendering logic, can benefit from breaking down into smaller components.

- **Tightly-Coupled Logic**:
  - **LinkPageRenderer & EnhancedPreview**: Coupled theme/styling logic across multiple components.

### Opportunities for Reusable Abstractions
1. **Form Wrapper**:
   - Extract form logic from Admin Page's link and social media inputs into a reusable form component.

2. **DataTable Component**:
   - Create a reusable DataTable component to display lists of links and social media items consistently.

3. **Modal**:
   - Standardize modals (currently only dialogs) across the app for consistent styling and behavior.

4. **Styling Themes**:
   - Centralize theme management to avoid redundancy and ensure consistency in color application across components.

Generated on: 2025-07-30 15:07:16

## Refactor Task List

- [ ] Decompose Admin page
- [ ] Decompose Main page
- [ ] Extract shared hooks
- [ ] Extract shared utilities
- [ ] UI component consolidation
- [ ] Testing setup
