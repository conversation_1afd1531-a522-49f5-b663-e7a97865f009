/* Link Page Preview Styles - Adapted from link-page/css/style.css */

/* CSS Variables for theming */
.link-page-preview {
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --secondary-color: #8b5cf6;
  --background-color: #ffffff;
  --surface-color: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --border-radius: 0.75rem;
  --transition: all 0.3s ease;
}

/* Dark theme support */
.link-page-preview[data-theme="dark"] {
  --background-color: #0f172a;
  --surface-color: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --border-color: #334155;
}

/* Base styles */
.link-page-preview {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  transition: var(--transition);
  min-height: 100vh;
  padding: 2rem 0;
}

/* Container */
.link-page-preview .container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Profile Section */
.link-page-preview .profile-section {
  text-align: center;
  padding: 2rem 0;
}

.link-page-preview .profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  border: 4px solid var(--primary-color);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  background-color: var(--surface-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.link-page-preview .profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.link-page-preview .profile-avatar i {
  font-size: 3rem;
  color: var(--primary-color);
}

.link-page-preview .profile-name {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.link-page-preview .profile-bio {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Links Section */
.link-page-preview .links-section {
  margin-bottom: 3rem;
}

.link-page-preview .link-item {
  display: block;
  width: 100%;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  background-color: var(--surface-color);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  text-align: center;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.link-page-preview .link-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.link-page-preview .link-item:active {
  transform: translateY(0);
}

.link-page-preview .link-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.link-page-preview .link-item:hover::before {
  left: 100%;
}

/* Social Media Section */
.link-page-preview .social-section {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.link-page-preview .social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: var(--surface-color);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  font-size: 1.2rem;
  cursor: pointer;
}

.link-page-preview .social-link:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Animations */
.link-page-preview .fade-in {
  animation: fadeIn 0.6s ease-out;
}

.link-page-preview .slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .link-page-preview .profile-name {
    font-size: 1.5rem;
  }

  .link-page-preview .profile-bio {
    font-size: 1rem;
  }

  .link-page-preview .social-section {
    gap: 0.75rem;
  }

  .link-page-preview .social-link {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .link-page-preview .container {
    padding: 0 0.75rem;
  }

  .link-page-preview .profile-avatar {
    width: 100px;
    height: 100px;
  }

  .link-page-preview .link-item {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
  }

  .link-page-preview .social-section {
    gap: 0.5rem;
  }

  .link-page-preview .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Theme variations */
.link-page-preview.theme-modern {
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --border-radius: 0.5rem;
}

.link-page-preview.theme-rounded {
  --primary-color: #ec4899;
  --primary-hover: #db2777;
  --border-radius: 2rem;
}

.link-page-preview.theme-minimal {
  --primary-color: #374151;
  --primary-hover: #1f2937;
  --border-radius: 0.25rem;
}

.link-page-preview.theme-vibrant {
  --primary-color: #f59e0b;
  --primary-hover: #d97706;
  --border-radius: 1rem;
}
