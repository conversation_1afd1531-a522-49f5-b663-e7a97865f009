import { useState, useEffect, useCallback } from 'react';
import { LinkPageData } from '@/types/LinkPageData';

export const useLinkData = (filename: string) => {
  const [linkData, setLinkData] = useState<LinkPageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLinkData = useCallback(async () => {
    console.log('fetchLinkData called with filename:', filename);
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`/api/link-data?filename=${filename}`);
      console.log('API response status:', response.status);

      if (!response.ok) {
        throw new Error("Failed to fetch data");
      }

      const data: LinkPageData = await response.json();
      console.log('Data fetched successfully:', !!data);
      setLinkData(data);
    } catch (err) {
      console.error('Error in fetchLinkData:', err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setLoading(false);
    }
  }, [filename]);

  useEffect(() => {
    fetchLinkData();
  }, [fetchLinkData]);

  return {
    linkData,
    loading,
    error,
    refetch: fetchLinkData
  };
};
