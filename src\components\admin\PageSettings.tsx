import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardT<PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Pages, UserInfo } from "@/types/admin";
import { privacyPolicyTemplate } from "@/constants/privacy-policy";
import { termsOfServiceTemplate } from "@/constants/terms-of-service";

interface PageSettingsProps {
  pages: Pages;
  userInfo: UserInfo;
  onUpdatePage: (
    page: keyof Pages,
    field: "title" | "content",
    value: string
  ) => void;
}

interface TemplateData {
  companyName: string;
  country: string;
  websiteName: string;
  websiteUrl: string;
}

const PageSettings: React.FC<PageSettingsProps> = ({
  pages,
  userInfo,
  onUpdatePage,
}) => {
  const [templateData, setTemplateData] = useState<TemplateData>({
    companyName: "",
    country: "",
    websiteName: "",
    websiteUrl: "",
  });

  const handleTemplateDataChange = (
    field: keyof TemplateData,
    value: string
  ) => {
    setTemplateData((prev) => ({ ...prev, [field]: value }));
  };

  const generateContent = (type: "privacy" | "terms") => {
    const template =
      type === "privacy" ? privacyPolicyTemplate : termsOfServiceTemplate;
    const lastUpdated = new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    const populatedContent = template
      .replace(/{{name}}/g, userInfo.name)
      .replace(/{{email}}/g, "<EMAIL>") // Placeholder for user email
      .replace(/{{lastUpdated}}/g, lastUpdated)
      .replace(/{{companyName}}/g, templateData.companyName)
      .replace(/{{country}}/g, templateData.country)
      .replace(/{{websiteName}}/g, templateData.websiteName)
      .replace(/{{websiteUrl}}/g, templateData.websiteUrl);

    onUpdatePage(type, "content", populatedContent);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Legal Pages</CardTitle>
        <CardDescription>
          Manage your privacy policy and terms of service. You can use our
          templates to get started.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Template Data Section */}
        <div className="space-y-4 p-4 border rounded-lg">
          <h3 className="text-lg font-semibold">Template Information</h3>
          <p className="text-sm text-muted-foreground">
            This information will be used to populate the legal page templates.
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="companyName">Company Name</Label>
              <Input
                id="companyName"
                value={templateData.companyName}
                onChange={(e) =>
                  handleTemplateDataChange("companyName", e.target.value)
                }
                placeholder="Your Company LLC"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                value={templateData.country}
                onChange={(e) =>
                  handleTemplateDataChange("country", e.target.value)
                }
                placeholder="United States"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="websiteName">Website Name</Label>
              <Input
                id="websiteName"
                value={templateData.websiteName}
                onChange={(e) =>
                  handleTemplateDataChange("websiteName", e.target.value)
                }
                placeholder="My Awesome Site"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="websiteUrl">Website URL</Label>
              <Input
                id="websiteUrl"
                value={templateData.websiteUrl}
                onChange={(e) =>
                  handleTemplateDataChange("websiteUrl", e.target.value)
                }
                placeholder="https://example.com"
              />
            </div>
          </div>
        </div>

        {/* Privacy Policy Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Privacy Policy</h3>
          <div className="space-y-2">
            <Label htmlFor="privacyTitle">Title</Label>
            <Input
              id="privacyTitle"
              value={pages.privacy.title}
              onChange={(e) => onUpdatePage("privacy", "title", e.target.value)}
              placeholder="Privacy Policy"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="privacyContent">Content</Label>
            <Textarea
              id="privacyContent"
              value={pages.privacy.content}
              onChange={(e) =>
                onUpdatePage("privacy", "content", e.target.value)
              }
              rows={8}
              placeholder="Enter your privacy policy here, or generate one."
            />
            <Button onClick={() => generateContent("privacy")} size="sm">
              Generate from Template
            </Button>
          </div>
        </div>

        {/* Terms of Service Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Terms of Service</h3>
          <div className="space-y-2">
            <Label htmlFor="termsTitle">Title</Label>
            <Input
              id="termsTitle"
              value={pages.terms.title}
              onChange={(e) => onUpdatePage("terms", "title", e.target.value)}
              placeholder="Terms of Service"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="termsContent">Content</Label>
            <Textarea
              id="termsContent"
              value={pages.terms.content}
              onChange={(e) =>
                onUpdatePage("terms", "content", e.target.value)
              }
              rows={8}
              placeholder="Enter your terms of service here, or generate one."
            />
            <Button onClick={() => generateContent("terms")} size="sm">
              Generate from Template
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PageSettings;
