import React from 'react';
import SimplePreview from '@/components/admin/SimplePreview';
import { LinkData } from '@/types/admin';

interface AdminPreviewProps {
  linkData: LinkData;
  onClose: () => void;
}

const AdminPreview: React.FC<AdminPreviewProps> = ({ linkData, onClose }) => {
  return (
    <div className="lg:sticky lg:top-6 lg:h-fit">
      <SimplePreview linkData={linkData} onClose={onClose} />
    </div>
  );
};

export default AdminPreview;
