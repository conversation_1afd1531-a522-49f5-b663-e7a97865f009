"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Card, CardContent } from "@/components/ui/card";
import { Check, Palette } from "lucide-react";

interface ColorPickerProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  presetColors?: string[];
}

const defaultPresetColors = [
  "#ffffff",
  "#f8fafc",
  "#f1f5f9",
  "#e2e8f0",
  "#000000",
  "#1f2937",
  "#374151",
  "#4b5563",
  "#ef4444",
  "#f97316",
  "#f59e0b",
  "#eab308",
  "#84cc16",
  "#22c55e",
  "#10b981",
  "#14b8a6",
  "#06b6d4",
  "#0ea5e9",
  "#3b82f6",
  "#6366f1",
  "#8b5cf6",
  "#a855f7",
  "#d946ef",
  "#ec4899",
];

export default function ColorPicker({
  label,
  value,
  onChange,
  presetColors = defaultPresetColors,
}: ColorPickerProps) {
  const [inputValue, setInputValue] = useState(value);

  const handleSubmit = () => {
    onChange(inputValue);
  };

  const handlePresetClick = (color: string) => {
    setInputValue(color);
    onChange(color);
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="w-full justify-start gap-2 h-10">
            <div
              className="w-4 h-4 rounded border"
              style={{ backgroundColor: value }}
            />
            <span className="text-sm">{value}</span>
            <Palette className="w-4 h-4 ml-auto opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm">Cor Personalizada</Label>
              <div className="flex gap-2">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="#000000"
                  className="flex-1"
                />
                <Button size="lg" onClick={handleSubmit}>
                  <Check className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm">Cores Pré-definidas</Label>
              <div className="grid grid-cols-8 gap-1">
                {presetColors.map((color) => (
                  <button
                    key={color}
                    className="w-6 h-6 rounded border border-gray-200 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => handlePresetClick(color)}
                    title={color}
                  />
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm">Cores Populares</Label>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { name: "Azul Profundo", value: "#1e40af" },
                  { name: "Verde Floresta", value: "#166534" },
                  { name: "Roxo Real", value: "#7c3aed" },
                  { name: "Rosa Vibrante", value: "#db2777" },
                  { name: "Laranja Queimado", value: "#ea580c" },
                  { name: "Cinza Moderno", value: "#374151" },
                ].map((color) => (
                  <Button
                    key={color.value}
                    variant="outline"
                    size="lg"
                    className="justify-start gap-2 h-8"
                    onClick={() => handlePresetClick(color.value)}
                  >
                    <div
                      className="w-3 h-3 rounded"
                      style={{ backgroundColor: color.value }}
                    />
                    <span className="text-xs truncate">{color.name}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
