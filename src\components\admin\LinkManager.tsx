import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { iconOptions } from "@/constants/admin";
import { LinkItem } from "@/types/admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Plus, Trash2 } from "lucide-react";

interface LinkManagerProps {
  links: LinkItem[];
  onAddLink: () => void;
  onRemoveLink: (id: string) => void;
  onUpdateLink: (id: string, field: keyof LinkItem, value: any) => void;
}

const LinkManager: React.FC<LinkManagerProps> = ({
  links,
  onAddLink,
  onRemoveLink,
  onUpdateLink,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Links
          <Button onClick={onAddLink} size="lg">
            <Plus className="w-4 h-4 mr-2" />
            Add Link
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {links.map((link) => (
          <div key={link.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <Switch
                checked={link.enabled}
                onCheckedChange={(checked) =>
                  onUpdateLink(link.id, "enabled", checked)
                }
              />
              <Button
                onClick={() => onRemoveLink(link.id)}
                variant="outline"
                size="lg"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            <div>
              <Label>Title</Label>
              <Input
                value={link.title}
                onChange={(e) => onUpdateLink(link.id, "title", e.target.value)}
                placeholder="Link title"
              />
            </div>
            <div>
              <Label>URL</Label>
              <Input
                value={link.url}
                onChange={(e) => onUpdateLink(link.id, "url", e.target.value)}
                placeholder="https://example.com"
              />
            </div>
            <div>
              <Label>Icon</Label>
              <Select
                value={link.icon}
                onValueChange={(value) => onUpdateLink(link.id, "icon", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {iconOptions.map((icon) => (
                    <SelectItem key={icon} value={icon}>
                      {icon}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        ))}
        {links.length === 0 && (
          <p className="text-center text-gray-500 py-8">
            No links added yet. Click "Add Link" to get started.
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default LinkManager;
