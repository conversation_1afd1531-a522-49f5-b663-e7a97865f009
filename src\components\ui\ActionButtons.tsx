import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, Plus } from "lucide-react";

interface ActionButtonsProps {
  onLoadData: () => void;
  onCreateNew: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onLoadData,
  onCreateNew,
}) => (
  <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-xs mx-auto">
    <Button
      variant="outline"
      onClick={onLoadData}
      className="flex-1"
      size={"lg"}
    >
      <Download className="mr-2 h-4 w-4" /> Load Data
    </Button>
    <Button onClick={onCreateNew} className="flex-1" size={"lg"}>
      <Plus className="mr-2 h-4 w-4" /> Create New
    </Button>
  </div>
);

export default ActionButtons;
