import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Upload, Download, Save } from "lucide-react";

interface MobileBottomNavigationProps {
  onExportData: () => void;
  onImportData: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSaveData: () => void;
}

const MobileBottomNavigation: React.FC<MobileBottomNavigationProps> = ({
  onExportData,
  onImportData,
  onSaveData,
}) => {
  return (
    <div className="fixed bottom-0 left-0 right-0 z-40 bg-white border-t shadow-lg">
      <div className="flex items-center justify-between px-4 py-3 gap-2">
        <Button asChild size="sm" variant="outline" className="min-h-[44px] px-3">
          <Link href="/" className="flex items-center gap-1">
            <ArrowLeft size={16} />
            <span className="text-xs">Back</span>
          </Link>
        </Button>
        
        <div className="flex gap-2">
          <Button
            onClick={onExportData}
            variant="outline"
            size="sm"
            className="min-h-[44px] min-w-[44px]"
            aria-label="Export data"
          >
            <Download size={16} />
          </Button>
          
          <label className="cursor-pointer">
            <Button
              variant="outline"
              size="sm"
              asChild
              className="min-h-[44px] min-w-[44px]"
              aria-label="Import data"
            >
              <span className="flex items-center justify-center">
                <Upload size={16} />
              </span>
            </Button>
            <input
              type="file"
              accept=".json"
              onChange={onImportData}
              className="hidden"
              aria-label="Import JSON file"
            />
          </label>
          
          <Button
            onClick={onSaveData}
            size="sm"
            className="min-h-[44px] px-3 bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            <Save size={16} className="mr-1" />
            <span className="text-xs">Save</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MobileBottomNavigation;
