import { useRef } from "react";
import { useRouter } from "next/navigation";

export const useLinkPageData = () => {
    const router = useRouter();
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const jsonData = JSON.parse(e.target?.result as string);
                    localStorage.setItem("pendingAdminData", JSON.stringify(jsonData));
                    router.push("/admin");
                } catch (error) {
                    alert("Error: Invalid JSON file. Please select a valid JSON file.");
                }
            };
            reader.onerror = () => {
                alert("Error reading file. Please try again.");
            };
            reader.readAsText(file);
        }
    };

    const loadFile = () => {
        fileInputRef.current?.click();
    };

    const createNew = async () => {
        try {
            const response = await fetch("/api/link-data?filename=links.json");
            if (response.ok) {
                const sampleData = await response.json();
                localStorage.setItem("pendingAdminData", JSON.stringify(sampleData));
                router.push("/admin");
            } else {
                alert("Error loading sample data. Please try again.");
            }
        } catch (error) {
            alert("Error loading sample data. Please try again.");
        }
    };

    return {
        fileInputRef,
        loadFile,
        createNew,
        handleFileChange,
    };
};