import React from "react";
import HomeHeader from "./HomeHeader";
import ActionButtons from "../ui/ActionButtons";
import { useLinkPageData } from "@/hooks/useLinkPageData";

const HomePageContainer = () => {
  const { fileInputRef, loadFile, createNew, handleFileChange } =
    useLinkPageData();

  return (
    <div className="min-h-screen w-full bg-[#fefcff] relative">
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "white",
          backgroundImage: `
     linear-gradient(to right, rgba(71,85,105,0.15) 1px, transparent 1px),
     linear-gradient(to bottom, rgba(71,85,105,0.15) 1px, transparent 1px),
     radial-gradient(circle at 50% 60%, rgba(236,72,153,0.15) 0%, rgba(168,85,247,0.05) 40%, transparent 70%)
   `,
          backgroundSize: "40px 40px, 40px 40px, 100% 100%",
        }}
      />
      <div className="absolute inset-0 flex items-center justify-center p-4">
        <div className="text-center max-w-xl">
          <HomeHeader />
          <ActionButtons onLoadData={loadFile} onCreateNew={createNew} />
          <div className="mt-8 space-y-2">
            <p className="text-gray-800">
              Not sure where to start? Check our tutorial:
            </p>
            <div className="flex justify-center space-x-4">
              <a
                href="/tutorial.html"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-gray-600 hover:text-blue-600 hover:underline"
              >
                Portuguese
              </a>
              <a
                href="/tutorial-en.html"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-gray-600 hover:text-blue-600 hover:underline"
              >
                English
              </a>
              <a
                href="/tutorial-de.html"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-gray-600 hover:text-blue-600 hover:underline"
              >
                German
              </a>
            </div>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleFileChange}
            style={{ display: "none" }}
          />
        </div>
      </div>
    </div>
  );
};

export default HomePageContainer;
