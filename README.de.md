# LinkHub - Erstellen Sie Ihre benutzerdefinierte Link-Seite

LinkHub ist eine Open-Source-<PERSON><PERSON><PERSON><PERSON>, mit der Sie Ihre eigene Link-Seite erstellen und anpassen können, ähnlich wie bei Diensten wie Linktree. Mit einer benutzerfreundlichen Verwaltungsoberfläche können Sie Ihre Links, Ihr Profil, Ihre sozialen Medien und Ihr Erscheinungsbild verwalten und dann eine statische Seite exportieren, die überall gehostet werden kann.

## ✨ Funktionen

- **🎨 Intuitiver visueller Editor:** Eine vollständige Verwaltungsoberfläche, um jeden Aspekt Ihrer Seite anzupassen, ohne eine einzige Codezeile zu berühren.
- **👤 Anpassbares Profil:** Fügen Sie Ihren Namen, Ihre Biografie und ein Profilbild hinzu.
- **🔗 Unbegrenzte Links:** Fügen Sie beliebig viele Links hinzu, bear<PERSON><PERSON>, ordnen und aktivieren/deaktivieren Sie sie.
- **📱 Social-Media-Symbole:** Fügen Sie Links zu Ihren sozialen Medien mit entsprechenden Symbolen hinzu.
- **🎨 Stilanpassung:** Ändern Sie Farben, Schriftarten, Ränder und sogar den Hintergrund (einfarbige Farben, Farbverläufe oder Bilder).
- **📝 Datenschutz- und Nutzungsbedingungsseiten:** Ein Editor zum Erstellen von Inhalten für die Seiten "Datenschutzrichtlinie" und "Nutzungsbedingungen".
- **🚀 Statischer Export:** Generieren Sie einen `link-page`-Ordner mit reinen HTML-, CSS- und JS-Dateien, der auf jedem statischen Hosting-Dienst (Netlify, Vercel, GitHub Pages usw.) gehostet werden kann.
- **💾 JSON-basiertes Management:** Alle Ihre Einstellungen werden in einer JSON-Datei gespeichert, was die Sicherung und Migration erleichtert.

## 🚀 Wie man es benutzt

Der Hauptworkflow ist in zwei Teile unterteilt: **Bearbeiten** im Admin-Panel und **Veröffentlichen** der statischen Seite.

### 1. Bearbeiten Ihrer Seite

1.  **Starten Sie die Anwendung:**

    ```bash
    # Abhängigkeiten installieren
    npm install

    # Entwicklungsserver starten
    npm run dev
    ```

    Öffnen Sie [http://localhost:3000/admin](http://localhost:3000/admin), um auf das Admin-Panel zuzugreifen.

2.  **Laden oder erstellen Sie Ihre Daten:** Sie können eine vorhandene JSON-Datei (wie `data/links.json`) laden oder von Grund auf neu beginnen, um Ihre Seite zu erstellen.

3.  **Passen Sie alles an:** Verwenden Sie die Registerkarten im Panel, um:

    - **Info:** Legen Sie Ihren Namen, Ihre Biografie und Ihre SEO-Einstellungen fest.
    - **Links:** Fügen Sie Ihre Links hinzu und verwalten Sie sie.
    - **Soziales:** Konfigurieren Sie Ihre Social-Media-Profile.
    - **Stile:** Ändern Sie das Erscheinungsbild Ihrer Seite.
    - **Seiten:** Bearbeiten Sie den Inhalt der Datenschutz- und Nutzungsbedingungsseiten.

4.  **Speichern Sie Ihre Daten:** Klicken Sie auf die Schaltfläche "Speichern" oder "Speichern und Exportieren". Dadurch werden Ihre Einstellungen in einer JSON-Datei im Ordner `/data` im Projektstamm gespeichert.

### 2. Veröffentlichen Ihrer Seite

Nach dem Speichern Ihrer Daten müssen Sie die Dateien für das Hosting vorbereiten.

1.  **Kopieren Sie den Ordner `link-page`:** Suchen Sie den Ordner `link-page` im Projektstamm und erstellen Sie eine Kopie an einem Ort Ihrer Wahl. Es ist **diese Kopie**, die Sie veröffentlichen werden.

2.  **Kopieren und benennen Sie Ihre JSON-Datei um:**

    - Holen Sie sich die von Ihnen gespeicherte JSON-Datei (z. B. `data/meine-links.json`).
    - Kopieren Sie diese Datei in den Ordner `data` Ihrer **Kopie** von `link-page`.
    - **Benennen Sie** die Datei in `links.json` um.

    Die endgültige Struktur Ihres kopierten Ordners sollte wie folgt aussehen:

    ```
    link-page/
    ├── css/
    ├── js/
    ├── assets/
    └── data/
        └── links.json  <-- Ihre umbenannte Datei
    └── index.html
    ```

3.  **Hosten Sie Ihren Ordner:** Laden Sie den Inhalt Ihres kopierten `link-page`-Ordners auf Ihren bevorzugten Hosting-Dienst hoch. Ihre Link-Seite wird live sein!

Eine detailliertere Schritt-für-Schritt-Anleitung finden Sie in unserer [**Benutzeranleitung**](/tutorial-de.html).

## 📁 Projektstruktur

```
.
├── data/              # Wo Ihre JSON-Datendateien gespeichert werden
├── link-page/         # Die Vorlage für die statische Seite, die gehostet werden soll
├── public/            # Öffentliche Dateien, einschließlich des Tutorials
└── src/
    └── app/
        └── admin/     # Wo sich die Admin-Oberfläche befindet
```

---

Mit ❤️ für die Entwicklergemeinschaft erstellt.
