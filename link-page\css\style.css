/* Reset e Variáveis CSS */
:root {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --secondary-color: #8b5cf6;
    --background-color: #ffffff;
    --surface-color: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 0.75rem;
    --transition: all 0.3s ease;
}

/* Tema Escuro */
[data-theme="dark"] {
    --background-color: #131313;
    --surface-color: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --border-color: #334155;
}

/* Reset Global */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Container */
.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Conteúdo Principal */
.main-content {
    flex: 1;
    padding: 2rem 0;
}

.page {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Seção de Perfil */
.profile-section {
    text-align: center;
    padding: 2rem 0;
}

.profile-avatar {
    width: 140px;
    height: 140px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    border: 4px solid var(--primary-color);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    background-color: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-avatar i {
    font-size: 3rem;
    color: var(--primary-color);
}

.profile-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.profile-bio {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Seção de Links */
.links-section {
    margin-bottom: 3rem;
}

.link-item {
    display: block;
    width: 100%;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    background-color: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.link-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.link-item:active {
    transform: translateY(0);
}

.link-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.link-item:hover::before {
    left: 100%;
}

/* Estilos de Ícones (FontAwesome & SVG) */
.link-item>i,
.link-item>svg {
    margin-right: 0.75rem;
    width: 1.2em;
    height: 1.2em;
    vertical-align: middle;
}

.social-link>i,
.social-link>svg {
    width: 1em;
    height: 1em;
    vertical-align: middle;
}


/* Seção de Redes Sociais */
.social-section {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background-color: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    font-size: 1.2rem;
}

.social-link:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Conteúdo Legal */
.legal-content {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
}

.legal-content h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.legal-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 2rem 0 1rem 0;
    color: var(--text-primary);
}

.legal-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 1.5rem 0 0.5rem 0;
    color: var(--text-primary);
}

.legal-content p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    line-height: 1.8;
}

.legal-content ul {
    margin-left: 2rem;
    margin-bottom: 1rem;
}

.legal-content li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.legal-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.legal-content a:hover {
    text-decoration: underline;
}

/* Rodapé */
.footer {
    padding: 2rem 0;
    margin-top: auto;
}

.footer-content {
    text-align: center;
}

.footer-content p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    color: var(--text-secondary);
}

.footer-links a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .profile-name {
        font-size: 1.5rem;
    }

    .profile-bio {
        font-size: 1rem;
    }

    .social-section {
        gap: 0.75rem;
    }

    .social-link {
        width: 60px;
        height: 60px;
        font-size: 1.1rem;
    }

    .legal-content {
        padding: 1.5rem;
    }

    .legal-content h1 {
        font-size: 1.5rem;
    }

    .legal-content h2 {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.75rem;
    }

    .profile-avatar {
        width: 140px;
        height: 140px;
    }

    .link-item {
        padding: 1.25rem 1.5rem;
        font-size: 1rem;
    }

    .social-section {
        gap: 0.5rem;
    }

    .social-link {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.5rem;
    }

    .footer-links span {
        display: none;
    }
}

/* Animações Adicionais */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Estilos para Temas Personalizados */
.theme-modern {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --border-radius: 0.5rem;
}

.theme-rounded {
    --primary-color: #ec4899;
    --primary-hover: #db2777;
    --border-radius: 2rem;
}

.theme-minimal {
    --primary-color: #374151;
    --primary-hover: #1f2937;
    --border-radius: 0.25rem;
}

.theme-vibrant {
    --primary-color: #f59e0b;
    --primary-hover: #d97706;
    --border-radius: 1rem;
}

/* Efeitos de Hover Avançados */
.link-item.gradient-hover {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
}

.link-item.gradient-hover:hover {
    background: linear-gradient(45deg, var(--primary-hover), var(--secondary-color));
    transform: translateY(-3px) scale(1.02);
}

/* Scrollbar Personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Bottom Sheet */
.bottom-sheet {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    opacity: 0;
    pointer-events: none;
    align-items: center;
    flex-direction: column;
    justify-content: flex-end;
    transition: 0.1s linear;
    z-index: 1000;
}

.bottom-sheet.show {
    opacity: 1;
    pointer-events: auto;
}

.bottom-sheet .sheet-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    background: var(--background-color);
}

.bottom-sheet .content {
    width: 100%;
    position: relative;
    background: var(--surface-color);
    max-height: 90vh;
    height: 50vh;
    padding: 1.5rem 2rem;
    transform: translateY(100%);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    box-shadow: var(--shadow-lg);
    transition: 0.3s ease;
    display: flex;
    flex-direction: column;
}

.bottom-sheet.show .content {
    transform: translateY(0%);
}

.bottom-sheet.dragging .content {
    transition: none;
}

.bottom-sheet.fullscreen .content {
    border-radius: 0;
    overflow-y: hidden;
}

.bottom-sheet .header {
    display: flex;
    justify-content: center;
    padding-bottom: 1rem;
}

.header .drag-icon {
    cursor: grab;
    user-select: none;
    padding: 1rem;
    margin-top: -1rem;
}

.header .drag-icon span {
    height: 4px;
    width: 40px;
    display: block;
    background: var(--border-color);
    border-radius: 50px;
}

.bottom-sheet .body {
    height: 100%;
    overflow-y: auto;
    padding-right: 0.5rem; /* Space for scrollbar */
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--surface-color);
}

/* Custom scrollbar for the sheet body */
.bottom-sheet .body::-webkit-scrollbar {
    width: 8px;
}

.bottom-sheet .body::-webkit-scrollbar-track {
    background: var(--surface-color);
}

.bottom-sheet .body::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 4px;
    border: 2px solid var(--surface-color);
}

.bottom-sheet .body::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary-color);
}