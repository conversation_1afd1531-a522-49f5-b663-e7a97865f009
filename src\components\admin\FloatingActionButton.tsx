import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, EyeOff } from "lucide-react";

interface FloatingActionButtonProps {
  isPreviewVisible: boolean;
  onTogglePreview: () => void;
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  isPreviewVisible,
  onTogglePreview,
}) => {
  return (
    <Button
      onClick={onTogglePreview}
      className="fixed bottom-20 right-4 z-50 h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 bg-primary hover:bg-primary/90 border-0"
      size="icon"
      aria-label={isPreviewVisible ? "Hide preview" : "Show preview"}
    >
      {isPreviewVisible ? (
        <EyeOff className="h-6 w-6 text-primary-foreground" />
      ) : (
        <Eye className="h-6 w-6 text-primary-foreground" />
      )}
    </Button>
  );
};

export default FloatingActionButton;
