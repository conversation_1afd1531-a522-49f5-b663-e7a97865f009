import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filename = searchParams.get('filename') || 'links.json'

    // Security: Prevent directory traversal
    const safeFilename = filename.replace(/[^a-zA-Z0-9._-]/g, '')
    const filePath = path.join(process.cwd(), 'data', safeFilename)

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      )
    }

    // Read and parse JSON file
    const fileContent = fs.readFileSync(filePath, 'utf-8')
    const data = JSON.parse(fileContent)

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error reading link data:', error)
    return NextResponse.json(
      { error: 'Failed to read data' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { data, filename } = await request.json()

    if (!data || !filename) {
      return NextResponse.json(
        { error: 'Missing data or filename' },
        { status: 400 }
      )
    }

    // Security: Prevent directory traversal
    const safeFilename = filename.replace(/[^a-zA-Z0-9._-]/g, '')
    const filePath = path.join(process.cwd(), 'data', safeFilename)

    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), 'data')
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
    }

    // Try to write the file, if it fails due to permissions, try with a different filename
    try {
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2))
    } catch (writeError) {
      console.error('Permission error writing to file, trying alternative filename:', writeError)
      // Try with a user-specific filename
      const alternativeFilename = `user_${safeFilename}`
      const alternativeFilePath = path.join(process.cwd(), 'data', alternativeFilename)
      fs.writeFileSync(alternativeFilePath, JSON.stringify(data, null, 2))
      return NextResponse.json({
        success: true,
        message: 'Data saved successfully with alternative filename',
        filename: alternativeFilename
      })
    }

    return NextResponse.json({ success: true, message: 'Data saved successfully' })
  } catch (error) {
    console.error('Error saving link data:', error)
    return NextResponse.json(
      { error: 'Failed to save data' },
      { status: 500 }
    )
  }
}