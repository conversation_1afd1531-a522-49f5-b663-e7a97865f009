import React, { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Styles } from "@/types/admin";

interface LayoutVisualEditorProps {
  layout: Styles["layout"];
  onUpdateLayout: (field: keyof Styles["layout"], value: string) => void;
}

const LayoutVisualEditor: React.FC<LayoutVisualEditorProps> = ({
  layout,
  onUpdateLayout,
}) => {
  const [activeTab, setActiveTab] = useState<"desktop" | "tablet" | "mobile">(
    "desktop"
  );
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const parseNumericValue = (value: string) => {
    return parseInt(value.replace("px", "")) || 0;
  };

  const formatSliderValue = (value: number, suffix: string = "px") => {
    return `${value}${suffix}`;
  };

  const getViewportSize = () => {
    switch (activeTab) {
      case "mobile":
        return "375px";
      case "tablet":
        return "768px";
      case "desktop":
        return "100%";
      default:
        return "100%";
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const width = Math.max(320, Math.min(800, x));

    onUpdateLayout("maxWidth", formatSliderValue(width));
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mouseup", handleMouseUp);
      return () => document.removeEventListener("mouseup", handleMouseUp);
    }
  }, [isDragging]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Editor Visual de Layout</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as any)}
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="desktop">Desktop</TabsTrigger>
            <TabsTrigger value="tablet">Tablet</TabsTrigger>
            <TabsTrigger value="mobile">Mobile</TabsTrigger>
          </TabsList>

          <TabsContent value="desktop" className="space-y-4">
            <div className="border rounded-lg p-4 bg-muted/20">
              <div className="flex items-center justify-center">
                <div
                  ref={containerRef}
                  className="relative bg-background border-2 border-dashed border-muted-foreground/30 transition-all duration-200"
                  style={{
                    maxWidth: layout.maxWidth,
                    borderRadius: layout.borderRadius,
                    padding: layout.spacing,
                    width: "100%",
                  }}
                >
                  <div className="space-y-3">
                    <div className="h-12 bg-muted rounded" />
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-4 bg-muted rounded w-1/2" />
                    <div className="grid grid-cols-2 gap-2">
                      <div className="h-10 bg-primary/20 rounded" />
                      <div className="h-10 bg-secondary/20 rounded" />
                    </div>
                  </div>

                  {/* Resize handle */}
                  <div
                    className="absolute -right-2 top-0 bottom-0 w-1 bg-muted-foreground/20 cursor-ew-resize hover:bg-muted-foreground/40 transition-colors"
                    onMouseDown={() => setIsDragging(true)}
                    onMouseMove={handleMouseMove}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="tablet" className="space-y-4">
            <div className="border rounded-lg p-4 bg-muted/20">
              <div className="flex items-center justify-center">
                <div
                  className="bg-background border-2 border-dashed border-muted-foreground/30 transition-all duration-200"
                  style={{
                    maxWidth: "768px",
                    borderRadius: layout.borderRadius,
                    padding: layout.spacing,
                    width: "100%",
                  }}
                >
                  <div className="space-y-3">
                    <div className="h-10 bg-muted rounded" />
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-8 bg-primary/20 rounded" />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="mobile" className="space-y-4">
            <div className="border rounded-lg p-4 bg-muted/20">
              <div className="flex items-center justify-center">
                <div
                  className="bg-background border-2 border-dashed border-muted-foreground/30 transition-all duration-200"
                  style={{
                    maxWidth: "375px",
                    borderRadius: layout.borderRadius,
                    padding: layout.spacing,
                    width: "100%",
                  }}
                >
                  <div className="space-y-2">
                    <div className="h-8 bg-muted rounded" />
                    <div className="h-3 bg-muted rounded w-3/4" />
                    <div className="h-10 bg-primary/20 rounded" />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="grid grid-cols-3 gap-4 mt-4">
          <div>
            <Label className="text-sm">Largura</Label>
            <Slider
              min={320}
              max={800}
              step={40}
              value={[parseNumericValue(layout.maxWidth)]}
              onValueChange={([value]) =>
                onUpdateLayout("maxWidth", formatSliderValue(value))
              }
            />
          </div>

          <div>
            <Label className="text-sm">Arredondamento</Label>
            <Slider
              min={0}
              max={32}
              step={4}
              value={[parseNumericValue(layout.borderRadius)]}
              onValueChange={([value]) =>
                onUpdateLayout("borderRadius", formatSliderValue(value))
              }
            />
          </div>

          <div>
            <Label className="text-sm">Espaçamento</Label>
            <Slider
              min={8}
              max={40}
              step={4}
              value={[parseNumericValue(layout.spacing)]}
              onValueChange={([value]) =>
                onUpdateLayout("spacing", formatSliderValue(value))
              }
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LayoutVisualEditor;
