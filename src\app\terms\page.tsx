"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";

interface TermsData {
  title: string;
  content: string;
}

interface LinkPageData {
  pages: {
    terms: TermsData;
  };
  styles: {
    colors: {
      background: string;
      text: string;
      cardBackground: string;
      border: string;
    };
    typography: {
      fontFamily: string;
    };
  };
}

export default function TermsPage() {
  const [data, setData] = useState<LinkPageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch("/api/link-data?filename=links.json");

      if (!response.ok) {
        throw new Error("Failed to fetch data");
      }

      const linkData = await response.json();
      setData(linkData);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Error</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchData}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">No Data Found</h2>
            <p className="text-muted-foreground mb-4">
              Please check your data file.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { pages, styles } = data;
  const termsData = pages.terms;

  return (
    <div
      className="min-h-screen p-4 sm:p-6"
      style={{
        backgroundColor: styles.colors.background,
        fontFamily: styles.typography.fontFamily,
      }}
    >
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="outline" asChild>
            <Link href="/" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Links
            </Link>
          </Button>
        </div>

        {/* Terms Content */}
        <Card
          className="w-full"
          style={{
            backgroundColor: styles.colors.cardBackground,
            borderColor: styles.colors.border,
          }}
        >
          <CardHeader>
            <CardTitle
              className="text-xl sm:text-2xl font-bold"
              style={{ color: styles.colors.text }}
            >
              {termsData.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className="prose prose-sm sm:prose-base max-w-none"
              style={{ color: styles.colors.text }}
              dangerouslySetInnerHTML={{ __html: termsData.content }}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
