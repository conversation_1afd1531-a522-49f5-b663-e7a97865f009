import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { LinkData } from "@/types/admin";

interface SimplePreviewProps {
  linkData: LinkData;
  onClose: () => void;
}

const SimplePreview: React.FC<SimplePreviewProps> = ({ linkData, onClose }) => {
  const { userInfo, links, socialMedia, styles } = linkData;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Preview
          <Button onClick={onClose} variant="outline" size="lg">
            <X className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div
          className="mx-auto p-6 rounded-lg border-2 border-dashed border-gray-300"
          style={{
            maxWidth: styles.layout.maxWidth,
            backgroundColor: styles.colors.background,
            color: styles.colors.text,
            fontFamily: styles.typography.fontFamily,
          }}
        >
          <div className="text-center mb-6">
            {userInfo.avatar && (
              <img
                src={userInfo.avatar}
                alt={userInfo.name}
                className="w-24 h-24 rounded-full mx-auto mb-4 border-4"
                style={{
                  borderColor: styles.colors.primary,
                }}
              />
            )}
            <h1
              className="text-2xl font-bold mb-2"
              style={{
                fontSize: styles.typography.headingSize,
              }}
            >
              {userInfo.name || "Seu Nome"}
            </h1>
            <p className="text-gray-600" style={{ color: styles.colors.text }}>
              {userInfo.bio || "Sua bio aparecerá aqui"}
            </p>
          </div>

          <div className="space-y-3">
            {links
              .filter((link) => link.enabled)
              .map((link) => (
                <a
                  key={link.id}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-3 rounded-lg text-center font-medium transition-all hover:opacity-90"
                  style={{
                    backgroundColor: styles.colors.primary,
                    color: styles.colors.background,
                    borderRadius: styles.layout.borderRadius,
                  }}
                >
                  {link.title || "Título do Link"}
                </a>
              ))}
          </div>

          {socialMedia.filter((social) => social.enabled).length > 0 && (
            <div
              className="mt-6 pt-6 border-t"
              style={{ borderColor: styles.colors.border }}
            >
              <div className="flex justify-center gap-4">
                {socialMedia
                  .filter((social) => social.enabled)
                  .map((social) => (
                    <a
                      key={social.id}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 rounded-full flex items-center justify-center transition-all hover:opacity-80"
                      style={{
                        backgroundColor: styles.colors.cardBackground,
                        border: `1px solid ${styles.colors.border}`,
                      }}
                    >
                      <span
                        className="text-lg"
                        style={{ color: styles.colors.text }}
                      >
                        {social.platform.charAt(0)}
                      </span>
                    </a>
                  ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SimplePreview;
