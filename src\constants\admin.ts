import { Theme } from '@/types/admin';

export const themes: Theme[] = [
  {
    name: "Light",
    value: "light",
    colors: {
      background: "#ffffff",
      text: "#1f2937",
      primary: "#3b82f6",
      primaryHover: "#2563eb",
      cardBackground: "#f9fafb",
      border: "#e5e7eb",
    },
  },
  {
    name: "Dark",
    value: "dark",
    colors: {
      background: "#1f2937",
      text: "#f9fafb",
      primary: "#60a5fa",
      primaryHover: "#3b82f6",
      cardBackground: "#374151",
      border: "#4b5563",
    },
  },
  {
    name: "Blue",
    value: "blue",
    colors: {
      background: "#eff6ff",
      text: "#1e3a8a",
      primary: "#2563eb",
      primaryHover: "#1d4ed8",
      cardBackground: "#dbeafe",
      border: "#bfdbfe",
    },
  },
  {
    name: "Green",
    value: "green",
    colors: {
      background: "#f0fdf4",
      text: "#14532d",
      primary: "#16a34a",
      primaryHover: "#15803d",
      cardBackground: "#dcfce7",
      border: "#bbf7d0",
    },
  },
  {
    name: "Baby Blue",
    value: "baby-blue",
    colors: {
      background: "#f0f9ff",
      text: "#1e40af",
      primary: "#3b82f6",
      primaryHover: "#2563eb",
      cardBackground: "#dbeafe",
      border: "#bfdbfe",
    },
  },
  {
    name: "Lavender",
    value: "lavender",
    colors: {
      background: "#f5f3ff",
      text: "#5b21b6",
      primary: "#8b5cf6",
      primaryHover: "#7c3aed",
      cardBackground: "#ede9fe",
      border: "#ddd6fe",
    },
  },
  {
    name: "Mint Green",
    value: "mint",
    colors: {
      background: "#f0fdf4",
      text: "#065f46",
      primary: "#10b981",
      primaryHover: "#059669",
      cardBackground: "#d1fae5",
      border: "#bbf7d0",
    },
  },
  {
    name: "Peach",
    value: "peach",
    colors: {
      background: "#fff7ed",
      text: "#7c2d12",
      primary: "#f97316",
      primaryHover: "#ea580c",
      cardBackground: "#ffedd5",
      border: "#fed7aa",
    },
  },
  {
    name: "Light Yellow",
    value: "light-yellow",
    colors: {
      background: "#fefce8",
      text: "#854d0e",
      primary: "#eab308",
      primaryHover: "#ca8a04",
      cardBackground: "#fef9c3",
      border: "#fef08a",
    },
  },
  {
    name: "Elegant Gray",
    value: "elegant-gray",
    colors: {
      background: "#f9fafb",
      text: "#111827",
      primary: "#6b7280",
      primaryHover: "#4b5563",
      cardBackground: "#f3f4f6",
      border: "#d1d5db",
    },
  },
  {
    name: "Purple",
    value: "purple",
    colors: {
      background: "#faf5ff",
      text: "#581c87",
      primary: "#9333ea",
      primaryHover: "#7c3aed",
      cardBackground: "#f3e8ff",
      border: "#e9d5ff",
    },
  },
  {
    name: "Pink",
    value: "pink",
    colors: {
      background: "#fdf2f8",
      text: "#831843",
      primary: "#ec4899",
      primaryHover: "#db2777",
      cardBackground: "#fce7f3",
      border: "#fbcfe8",
    },
  },
  {
    name: "Red",
    value: "red",
    colors: {
      background: "#fef2f2",
      text: "#7f1d1d",
      primary: "#ef4444",
      primaryHover: "#dc2626",
      cardBackground: "#fee2e2",
      border: "#fecaca",
    },
  },
  {
    name: "Orange",
    value: "orange",
    colors: {
      background: "#fff7ed",
      text: "#7c2d12",
      primary: "#f97316",
      primaryHover: "#ea580c",
      cardBackground: "#ffedd5",
      border: "#fed7aa",
    },
  },
  {
    name: "Yellow",
    value: "yellow",
    colors: {
      background: "#fefce8",
      text: "#854d0e",
      primary: "#eab308",
      primaryHover: "#ca8a04",
      cardBackground: "#fef9c3",
      border: "#fef08a",
    },
  },
  {
    name: "Cyan",
    value: "cyan",
    colors: {
      background: "#ecfeff",
      text: "#0e7490",
      primary: "#06b6d4",
      primaryHover: "#0891b2",
      cardBackground: "#cffafe",
      border: "#a5f3fc",
    },
  },
  {
    name: "Teal",
    value: "teal",
    colors: {
      background: "#f0fdfa",
      text: "#0f766e",
      primary: "#14b8a6",
      primaryHover: "#0d9488",
      cardBackground: "#ccfbf1",
      border: "#99f6e4",
    },
  },
  {
    name: "Gray",
    value: "gray",
    colors: {
      background: "#f9fafb",
      text: "#1f2937",
      primary: "#6b7280",
      primaryHover: "#4b5563",
      cardBackground: "#f3f4f6",
      border: "#e5e7eb",
    },
  },
  {
    name: "Slate",
    value: "slate",
    colors: {
      background: "#f8fafc",
      text: "#0f172a",
      primary: "#64748b",
      primaryHover: "#475569",
      cardBackground: "#f1f5f9",
      border: "#e2e8f0",
    },
  },
  {
    name: "Stone",
    value: "stone",
    colors: {
      background: "#fafaf9",
      text: "#1c1917",
      primary: "#78716c",
      primaryHover: "#57534e",
      cardBackground: "#f5f5f4",
      border: "#e7e5e4",
    },
  },
  {
    name: "Neutral",
    value: "neutral",
    colors: {
      background: "#fafafa",
      text: "#171717",
      primary: "#737373",
      primaryHover: "#525252",
      cardBackground: "#f5f5f5",
      border: "#e5e5e5",
    },
  },
  {
    name: "Zinc",
    value: "zinc",
    colors: {
      background: "#fafafa",
      text: "#18181b",
      primary: "#71717a",
      primaryHover: "#52525b",
      cardBackground: "#f4f4f5",
      border: "#e4e4e7",
    },
  },
];

export const colorOptions = [
  // Vibrantes
  "#3b82f6",
  "#ef4444",
  "#10b981",
  "#f59e0b",
  "#8b5cf6",
  "#ec4899",
  "#06b6d4",
  "#84cc16",
  "#f97316",
  "#6366f1",
  "#14b8a6",
  "#f43f5e",

  // Adicionais vibrantes
  "#0ea5e9",
  "#22c55e",
  "#eab308",
  "#a855f7",
  "#fb7185",

  // Tons escuros
  "#64748b",
  "#6b7280",
  "#475569",
  "#334155",
  "#1e293b",
  "#0f172a",

  // Pastel
  "#fbcfe8",
  "#bfdbfe",
  "#bbf7d0",
  "#fef3c7",
  "#ddd6fe",
  "#e0f2fe",

  // Suaves e naturais
  "#f5f5f4",
  "#e7e5e4",
  "#d6d3d1",
  "#a8a29e",
  "#78716c",
  "#57534e",

  // Neons
  "#00f0ff",
  "#ff00c8",
  "#39ff14",
  "#ff3131",
  "#ffd700",
  "#8aff00",

  // Metálicos / Luxo
  "#d4af37",
  "#c0c0c0",
  "#b87333",
  "#a9a9a9",
  "#999999",
  "#343434",
];

export const iconOptions = [
  "Github",
  "Linkedin",
  "Twitter",
  "Instagram",
  "Youtube",
  "Facebook",
  "Mail",
  "Phone",
  "MapPin",
  "Globe",
  "Briefcase",
  "FileText",
  "Music",
  "Camera",
  "Coffee",
  "Heart",
  "Star",
  "BookOpen",
  "Code",
  "Database",
  "Server",
  "Cloud",
  "Lock",
  "Shield",
  "Zap",
  "Battery",
  "Wifi",
  "Bluetooth",
  "Usb",
  "Monitor",
];

export const backgroundOptions = [
  { name: "Gradient Top", value: "bg-gradient-top" },
  { name: "Dots", value: "bg-dots" },
  { name: "Dreamy", value: "bg-dreamy" },
  { name: "Grid", value: "bg-grid" },
  { name: "Gradient Bottom", value: "bg-gradient-bottom" },
  { name: "Waves", value: "bg-waves" },
  { name: "Bubbles", value: "bg-bubbles" },
];
