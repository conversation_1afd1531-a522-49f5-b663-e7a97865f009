"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>rk<PERSON>, Zap, Heart } from "lucide-react";

interface Template {
  id: string;
  name: string;
  description: string;
  colors: {
    background: string;
    text: string;
    primary: string;
    cardBackground: string;
  };
  icon: React.ComponentType<any>;
  category: string;
}

interface TemplateSelectorProps {
  onSelect: (template: Template) => void;
}

const templates: Template[] = [
  {
    id: "modern",
    name: "Moderno Minimalista",
    description: "Design limpo com cores suaves e tipografia elegante",
    colors: {
      background: "#ffffff",
      text: "#1f2937",
      primary: "#3b82f6",
      cardBackground: "#f9fafb",
    },
    icon: Sparkles,
    category: "Popular",
  },
  {
    id: "dark",
    name: "<PERSON><PERSON>ro",
    description: "Tema escuro elegante com alto contraste",
    colors: {
      background: "#111827",
      text: "#f9fafb",
      primary: "#60a5fa",
      cardBackground: "#1f2937",
    },
    icon: Zap,
    category: "Tema",
  },
  {
    id: "warm",
    name: "Acolhedor",
    description: "Cores quentes e convidativas",
    colors: {
      background: "#fef3c7",
      text: "#92400e",
      primary: "#f59e0b",
      cardBackground: "#ffffff",
    },
    icon: Heart,
    category: "Popular",
  },
  {
    id: "nature",
    name: "Natureza",
    description: "Verdes e terrosos inspirados na natureza",
    colors: {
      background: "#f0fdf4",
      text: "#166534",
      primary: "#22c55e",
      cardBackground: "#ffffff",
    },
    icon: Palette,
    category: "Tema",
  },
  {
    id: "ocean",
    name: "Oceano",
    description: "Azuis profundos como o mar",
    colors: {
      background: "#eff6ff",
      text: "#1e40af",
      primary: "#0ea5e9",
      cardBackground: "#ffffff",
    },
    icon: Sparkles,
    category: "Tema",
  },
  {
    id: "sunset",
    name: "Pôr do Sol",
    description: "Laranjas e rosas vibrantes",
    colors: {
      background: "#fff7ed",
      text: "#7c2d12",
      primary: "#f97316",
      cardBackground: "#ffffff",
    },
    icon: Heart,
    category: "Popular",
  },
];

export default function TemplateSelector({ onSelect }: TemplateSelectorProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Palette className="w-5 h-5" />
        <h3 className="text-lg font-semibold">Escolha um Template</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => {
          const Icon = template.icon;
          return (
            <Card
              key={template.id}
              className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-primary/50"
              onClick={() => onSelect(template)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon className="w-5 h-5 text-primary" />
                    <CardTitle className="text-sm">{template.name}</CardTitle>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {template.category}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-xs text-muted-foreground">
                  {template.description}
                </p>

                {/* Color Preview */}
                <div className="flex gap-1">
                  <div
                    className="w-full h-6 rounded border"
                    style={{ backgroundColor: template.colors.background }}
                    title="Background"
                  />
                  <div
                    className="w-full h-6 rounded border"
                    style={{ backgroundColor: template.colors.text }}
                    title="Text"
                  />
                  <div
                    className="w-full h-6 rounded border"
                    style={{ backgroundColor: template.colors.primary }}
                    title="Primary"
                  />
                  <div
                    className="w-full h-6 rounded border"
                    style={{ backgroundColor: template.colors.cardBackground }}
                    title="Card"
                  />
                </div>

                <Button size="lg" className="w-full" variant="outline">
                  Usar Template
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
