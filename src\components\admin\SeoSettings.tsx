import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { LinkData } from "@/types/admin";

interface SeoSettingsProps {
  seo: LinkData["seo"];
  onUpdateSEO: (field: keyof LinkData["seo"], value: string) => void;
}

const SeoSettings: React.FC<SeoSettingsProps> = ({ seo, onUpdateSEO }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>SEO</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="seoTitle">Title</Label>
          <Input
            id="seoTitle"
            value={seo.title}
            onChange={(e) => onUpdateSEO("title", e.target.value)}
            placeholder="Page title for search engines"
          />
        </div>
        <div>
          <Label htmlFor="seoDescription">Description</Label>
          <Textarea
            id="seoDescription"
            value={seo.description}
            onChange={(e) => onUpdateSEO("description", e.target.value)}
            rows={2}
            placeholder="Brief description for search results"
          />
        </div>
        <div>
          <Label htmlFor="seoKeywords">Keywords</Label>
          <Input
            id="seoKeywords"
            value={seo.keywords}
            onChange={(e) => onUpdateSEO("keywords", e.target.value)}
            placeholder="keyword1, keyword2, keyword3"
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default SeoSettings;
