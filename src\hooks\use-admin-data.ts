import { useState, useEffect, useCallback } from 'react';
import { LinkData } from '@/types/admin';
import { themes } from '@/constants/admin';

export const useAdminData = (initialFilename: string) => {
  const [linkData, setLinkData] = useState<LinkData>({
    userInfo: { name: '', bio: '', avatar: '' },
    links: [],
    socialMedia: [],
    styles: {
      theme: 'light',
      colors: {
        background: '#ffffff',
        text: '#1f2937',
        primary: '#3b82f6',
        primaryHover: '#2563eb',
        cardBackground: '#f9fafb',
        border: '#e5e7eb',
      },
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: '16px',
        headingSize: '24px',
      },
      layout: { borderRadius: '12px', spacing: '16px', maxWidth: '480px' },
      background: { type: 'color', value: '#ffffff', image: '' },
    },
    seo: {
      title: '',
      description: '',
      keywords: '',
      favicon: '/favicon.ico',
      shareImage: '',
    },
    pages: {
      privacy: { title: 'Privacy Policy', content: '' },
      terms: { title: 'Terms of Service', content: '' },
    },
  });

  const [showPreview, setShowPreview] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [currentFilename, setCurrentFilename] = useState(initialFilename);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = useCallback(async () => {
    try {
      // Check for pending data in localStorage first
      const pendingData = localStorage.getItem('pendingAdminData');
      if (pendingData) {
        try {
          const data = JSON.parse(pendingData);
          setLinkData(data);
          // Clear the localStorage after loading
          localStorage.removeItem('pendingAdminData');
          setIsLoading(false);
          return;
        } catch (error) {
          console.error('Error parsing pending data:', error);
          // Continue to load from API if localStorage data is invalid
        }
      }

      // Fallback to loading from API
      const response = await fetch(`/api/link-data?filename=${currentFilename}`);
      if (response.ok) {
        const data = await response.json();
        setLinkData(data);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentFilename]);

  const saveData = async () => {
    try {
      const response = await fetch('/api/link-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ data: linkData, filename: currentFilename }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.filename && result.filename !== currentFilename) {
          setCurrentFilename(result.filename);
          console.log('Using alternative filename:', result.filename);
        }
        alert('Data saved successfully!');
      } else {
        alert('Error saving data');
      }
    } catch (error) {
      console.error('Error saving:', error);
      alert('Error saving data');
    }
  };

  const saveAndExportData = () => {
    const dataStr = JSON.stringify(linkData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'links.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const exportData = () => {
    const dataStr = JSON.stringify(linkData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'links.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const importData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          setLinkData(data);
          alert('Data imported successfully!');
        } catch (error) {
          alert('Error importing JSON file');
        }
      };
      reader.readAsText(file);
    }
  };

  const applyTheme = (themeValue: string) => {
    const theme = themes.find((t) => t.value === themeValue);
    if (theme) {
      setLinkData((prev) => ({
        ...prev,
        styles: {
          ...prev.styles,
          theme: themeValue,
          colors: theme.colors,
          background: {
            type: "color",
            value: theme.colors.background,
            image: "",
          },
        },
      }));
    }
  };

  return {
    linkData,
    setLinkData,
    showPreview,
    isLoading,
    setShowPreview,
    saveData,
    saveAndExportData,
    exportData,
    importData,
    applyTheme,
  };
};
