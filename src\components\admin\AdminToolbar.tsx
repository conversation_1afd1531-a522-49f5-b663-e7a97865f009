import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  User,
  Link as LinkI<PERSON>,
  <PERSON>hare2,
  <PERSON><PERSON>,
  FileText,
} from "lucide-react";

interface AdminToolbarProps {
  children: React.ReactNode;
}

const AdminToolbar: React.FC<AdminToolbarProps> = ({ children }) => {
  const isMobile = useIsMobile();

  return (
    <Tabs defaultValue="userInfo" className="w-full">
      <TabsList
        className={`
        grid w-full gap-1
        ${
          isMobile
            ? "grid-cols-2 h-auto p-1"
            : "grid-cols-2 sm:grid-cols-3 lg:grid-cols-5"
        }
      `}
      >
        <TabsTrigger
          value="userInfo"
          className={`
            flex items-center gap-1 sm:gap-2 text-xs sm:text-sm
            ${isMobile ? "min-h-[44px] flex-col py-2" : ""}
          `}
        >
          <User
            className={`${isMobile ? "w-5 h-5" : "w-3 h-3 sm:w-4 sm:h-4"}`}
          />
          <span className={`${isMobile ? "text-xs" : "hidden sm:inline"}`}>
            Profile
          </span>
          {!isMobile && <span className="sm:hidden">👤</span>}
        </TabsTrigger>
        <TabsTrigger
          value="links"
          className={`
            flex items-center gap-1 sm:gap-2 text-xs sm:text-sm
            ${isMobile ? "min-h-[44px] flex-col py-2" : ""}
          `}
        >
          <LinkIcon
            className={`${isMobile ? "w-5 h-5" : "w-3 h-3 sm:w-4 sm:h-4"}`}
          />
          <span className={`${isMobile ? "text-xs" : "hidden sm:inline"}`}>
            Links
          </span>
          {!isMobile && <span className="sm:hidden">🔗</span>}
        </TabsTrigger>
        <TabsTrigger
          value="social"
          className={`
            flex items-center gap-1 sm:gap-2 text-xs sm:text-sm
            ${isMobile ? "min-h-[44px] flex-col py-2" : ""}
          `}
        >
          <Share2
            className={`${isMobile ? "w-5 h-5" : "w-3 h-3 sm:w-4 sm:h-4"}`}
          />
          <span className={`${isMobile ? "text-xs" : "hidden sm:inline"}`}>
            Social
          </span>
          {!isMobile && <span className="sm:hidden">📱</span>}
        </TabsTrigger>
        <TabsTrigger
          value="styles"
          className={`
            flex items-center gap-1 sm:gap-2 text-xs sm:text-sm
            ${isMobile ? "min-h-[44px] flex-col py-2" : ""}
          `}
        >
          <Palette
            className={`${isMobile ? "w-5 h-5" : "w-3 h-3 sm:w-4 sm:h-4"}`}
          />
          <span className={`${isMobile ? "text-xs" : "hidden sm:inline"}`}>
            Styles
          </span>
          {!isMobile && <span className="sm:hidden">🎨</span>}
        </TabsTrigger>
        <TabsTrigger
          value="pages"
          className={`
            flex items-center gap-1 sm:gap-2 text-xs sm:text-sm
            ${isMobile ? "min-h-[44px] flex-col py-2" : ""}
          `}
        >
          <FileText
            className={`${isMobile ? "w-5 h-5" : "w-3 h-3 sm:w-4 sm:h-4"}`}
          />
          <span className={`${isMobile ? "text-xs" : "hidden sm:inline"}`}>
            Pages
          </span>
          {!isMobile && <span className="sm:hidden">📄</span>}
        </TabsTrigger>
      </TabsList>
      {children}
    </Tabs>
  );
};

export default AdminToolbar;
