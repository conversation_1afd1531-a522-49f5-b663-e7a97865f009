---
description: Repository Information Overview
alwaysApply: true
---

# Z.ai Code Scaffold Information

## Summary

A modern, production-ready web application scaffold built with Next.js, TypeScript, and Tailwind CSS. Designed to accelerate development with Z.ai's AI-powered coding assistance, featuring a comprehensive set of UI components, state management, and backend integration.

## Structure

- **src/app/**: Next.js App Router pages and API routes
- **src/components/**: Reusable React components including shadcn/ui components
- **src/hooks/**: Custom React hooks for state and UI management
- **src/lib/**: Utility functions and configurations
- **prisma/**: Database schema and Prisma ORM configuration
- **public/**: Static assets served by Next.js
- **data/**: Sample data files for development

## Language & Runtime

**Language**: TypeScript
**Version**: TypeScript 5.8.3
**Framework**: Next.js 15.4.5
**React Version**: 19.1.1
**Build System**: Next.js build system
**Package Manager**: npm/pnpm

## Dependencies

**Main Dependencies**:

- Next.js 15.4.5 (React framework)
- React 19.1.1 (UI library)
- Prisma 6.13.0 (ORM)
- Tailwind CSS 4.1.11 (CSS framework)
- shadcn/ui (UI component library based on Radix UI)
- Socket.io 4.8.1 (WebSocket implementation)
- TanStack Query 5.83.0 (Data fetching)
- Zustand 5.0.6 (State management)
- Zod 4.0.14 (Schema validation)

**Development Dependencies**:

- TypeScript 5.8.3
- ESLint 9.32.0
- Tailwind CSS 4.1.11
- Nodemon 3.1.10

## Build & Installation

```bash
# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start
```

## Database

**ORM**: Prisma 6.13.0
**Database**: SQLite (default configuration)
**Schema**: User and Post models defined in schema.prisma
**Commands**:

```bash
# Push schema changes to database
npm run db:push

# Generate Prisma client
npm run db:generate

# Create migrations
npm run db:migrate

# Reset database
npm run db:reset
```

## Server Configuration

**Server**: Custom Next.js server with Socket.IO integration
**Port**: 3000 (default)
**WebSocket**: Socket.IO implementation at /api/socketio
**Entry Point**: server.ts (production) or Next.js dev server (development)

## Features

- **UI Components**: Complete shadcn/ui component library
- **State Management**: Zustand for global state
- **Data Fetching**: TanStack Query for API requests
- **Forms**: React Hook Form with Zod validation
- **Styling**: Tailwind CSS with animations
- **WebSockets**: Real-time communication with Socket.IO
- **Database**: Prisma ORM with SQLite
- **Internationalization**: Next Intl support
- **Theming**: Dark/light mode with Next Themes
