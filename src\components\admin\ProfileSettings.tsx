import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { LinkData } from "@/types/admin";

interface ProfileSettingsProps {
  userInfo: LinkData["userInfo"];
  onUpdateUserInfo: (field: keyof LinkData["userInfo"], value: string) => void;
}

const ProfileSettings: React.FC<ProfileSettingsProps> = ({
  userInfo,
  onUpdateUserInfo,
}) => {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={userInfo.name}
              onChange={(e) => onUpdateUserInfo("name", e.target.value)}
              placeholder="Enter your name"
            />
          </div>
          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              value={userInfo.bio}
              onChange={(e) => onUpdateUserInfo("bio", e.target.value)}
              rows={3}
              placeholder="Tell us about yourself"
            />
          </div>
          <div>
            <Label htmlFor="avatar">Avatar URL</Label>
            <Input
              id="avatar"
              value={userInfo.avatar}
              onChange={(e) => onUpdateUserInfo("avatar", e.target.value)}
              placeholder="https://example.com/avatar.jpg"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileSettings;
