"use client";

import { useEffect, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import "../../styles/link-page-preview.css";

interface LinkItem {
  id: string;
  title: string;
  url: string;
  icon?: string;
  enabled: boolean;
}

interface SocialMediaItem {
  id: string;
  platform: string;
  url: string;
  icon: string;
  enabled: boolean;
}

interface UserInfo {
  name: string;
  bio: string;
  avatar: string;
}

interface Styles {
  theme: string;
  colors: {
    background: string;
    text: string;
    primary: string;
    primaryHover: string;
    cardBackground: string;
    border: string;
  };
  typography: {
    fontFamily: string;
    fontSize: string;
    headingSize: string;
  };
  layout: {
    borderRadius: string;
    spacing: string;
    maxWidth: string;
  };
  background: {
    type: string;
    value: string;
    image: string;
  };
}

interface EnhancedPreviewProps {
  data: {
    userInfo: UserInfo;
    links: LinkItem[];
    socialMedia: SocialMediaItem[];
    styles: Styles;
  };
}

// Font Awesome icon mapping
const getFontAwesomeIcon = (iconName: string): string => {
  const iconMap: { [key: string]: string } = {
    Link: "fas fa-link",
    Github: "fab fa-github",
    Linkedin: "fab fa-linkedin-in",
    Twitter: "fab fa-twitter",
    Instagram: "fab fa-instagram",
    Youtube: "fab fa-youtube",
    FileText: "fas fa-file-text",
    Briefcase: "fas fa-briefcase",
    ExternalLink: "fas fa-external-link-alt",
    Camera: "fas fa-camera",
    Home: "fas fa-home",
    User: "fas fa-user",
    Envelope: "fas fa-envelope",
    Phone: "fas fa-phone",
    Globe: "fas fa-globe",
    Blog: "fas fa-blog",
  };

  return iconMap[iconName] || "fas fa-link";
};

// Social media icon mapping
const getSocialIcon = (platform: string): string => {
  const socialIcons: { [key: string]: string } = {
    facebook: "fab fa-facebook-f",
    twitter: "fab fa-twitter",
    instagram: "fab fa-instagram",
    linkedin: "fab fa-linkedin-in",
    youtube: "fab fa-youtube",
    tiktok: "fab fa-tiktok",
    github: "fab fa-github",
    whatsapp: "fab fa-whatsapp",
    telegram: "fab fa-telegram",
    discord: "fab fa-discord",
    website: "fas fa-globe",
    blog: "fas fa-blog",
    portfolio: "fas fa-briefcase",
    contact: "fas fa-envelope",
    email: "fas fa-envelope",
    mail: "fas fa-envelope",
    phone: "fas fa-phone",
    mobile: "fas fa-mobile",
  };

  return socialIcons[platform.toLowerCase()] || "fas fa-link";
};

export default function EnhancedPreview({ data }: EnhancedPreviewProps) {
  const { userInfo, links, socialMedia, styles } = data;
  const previewRef = useRef<HTMLDivElement>(null);

  const enabledLinks = links.filter((link) => link.enabled);
  const enabledSocialMedia = socialMedia.filter((social) => social.enabled);

  // Apply theme styles using CSS custom properties
  useEffect(() => {
    if (previewRef.current && styles) {
      const element = previewRef.current;

      // Apply colors
      if (styles.colors) {
        element.style.setProperty(
          "--primary-color",
          styles.colors.primary || "#6366f1"
        );
        element.style.setProperty(
          "--primary-hover",
          styles.colors.primaryHover || "#4f46e5"
        );
        element.style.setProperty(
          "--background-color",
          styles.colors.background || "#ffffff"
        );
        element.style.setProperty(
          "--surface-color",
          styles.colors.cardBackground || "#f8fafc"
        );
        element.style.setProperty(
          "--text-primary",
          styles.colors.text || "#1e293b"
        );
        element.style.setProperty(
          "--border-color",
          styles.colors.border || "#e2e8f0"
        );
      }

      // Apply layout styles
      if (styles.layout) {
        element.style.setProperty(
          "--border-radius",
          styles.layout.borderRadius || "0.75rem"
        );
      }

      // Apply theme class
      if (styles.theme) {
        element.setAttribute("data-theme", styles.theme);
      }

      // Apply background
      if (styles.background) {
        if (styles.background.type === "image" && styles.background.image) {
          element.style.backgroundImage = `url(${styles.background.image})`;
          element.style.backgroundSize = "cover";
          element.style.backgroundPosition = "center";
        } else {
          element.style.backgroundImage = "none";
        }
      }
    }
  }, [styles]);

  // Escape HTML to prevent XSS
  const escapeHtml = (text: string): string => {
    if (!text) return "";
    const map: { [key: string]: string } = {
      "&": "&amp;",
      "<": "&lt;",
      ">": "&gt;",
      '"': "&quot;",
      "'": "&#039;",
    };
    return text.replace(/[&<>"']/g, (m) => map[m]);
  };

  return (
    <CardContent className="p-0">
      <div className="h-full overflow-auto">
        <div
          ref={previewRef}
          className="link-page-preview"
          style={{
            fontFamily: styles.typography?.fontFamily || "Inter, sans-serif",
          }}
        >
          <div className="container">
            {/* Profile Section */}
            <div className="profile-section">
              <div className="profile-avatar fade-in">
                {userInfo.avatar ? (
                  <img
                    src={userInfo.avatar}
                    alt={userInfo.name || "Avatar"}
                    onError={(e) => {
                      e.currentTarget.style.display = "none";
                      const fallback = e.currentTarget
                        .nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = "flex";
                    }}
                  />
                ) : null}
                <i
                  className="fas fa-user"
                  style={{ display: userInfo.avatar ? "none" : "flex" }}
                ></i>
              </div>
              {userInfo.name && (
                <h1 className="profile-name fade-in">
                  {escapeHtml(userInfo.name)}
                </h1>
              )}
              {userInfo.bio && (
                <p className="profile-bio fade-in">
                  {escapeHtml(userInfo.bio)}
                </p>
              )}
            </div>

            {/* Social Media Section */}
            {enabledSocialMedia.length > 0 && (
              <div className="social-section">
                {enabledSocialMedia.map((social, index) => (
                  <a
                    key={social.id}
                    href={social.url}
                    className="social-link slide-up"
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ animationDelay: `${index * 0.1}s` }}
                    title={social.platform}
                  >
                    <i className={getSocialIcon(social.platform)}></i>
                  </a>
                ))}
              </div>
            )}

            {/* Links Section */}
            <div className="links-section">
              {enabledLinks.length > 0 ? (
                enabledLinks.map((link, index) => (
                  <a
                    key={link.id}
                    href={link.url}
                    className="link-item slide-up"
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <i className={getFontAwesomeIcon(link.icon || "Link")}></i>
                    <span style={{ marginLeft: "0.5rem" }}>
                      {escapeHtml(link.title)}
                    </span>
                  </a>
                ))
              ) : (
                <div style={{ textAlign: "center", padding: "2rem 0" }}>
                  <div
                    style={{
                      color: "var(--text-secondary)",
                      fontSize: "0.875rem",
                    }}
                  >
                    Adicione links para vê-los aqui
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  );
}
