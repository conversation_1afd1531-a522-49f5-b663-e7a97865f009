import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON><PERSON>nt,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>lider } from "@/components/ui/slider";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { RotateCcw, Eye, EyeOff } from "lucide-react";
import { Styles } from "@/types/admin";

interface LayoutSettingsProps {
  layout: Styles["layout"];
  onUpdateLayout: (field: keyof Styles["layout"], value: string) => void;
}

interface LayoutPreset {
  name: string;
  maxWidth: string;
  borderRadius: string;
  spacing: string;
}

const layoutPresets: LayoutPreset[] = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", maxWidth: "320px", borderRadius: "8px", spacing: "12px" },
  { name: "<PERSON><PERSON><PERSON>", maxWidth: "480px", borderRadius: "12px", spacing: "16px" },
  { name: "Amplo", maxWidth: "600px", borderRadius: "16px", spacing: "24px" },
  { name: "Wide", maxWidth: "800px", borderRadius: "24px", spacing: "32px" },
];

const defaultLayout: Styles["layout"] = {
  maxWidth: "480px",
  borderRadius: "12px",
  spacing: "16px",
};

const LayoutSettings: React.FC<LayoutSettingsProps> = ({
  layout,
  onUpdateLayout,
}) => {
  const [showPreview, setShowPreview] = useState(true);
  const [activePreset, setActivePreset] = useState<string>("Padrão");

  const handlePresetChange = (preset: LayoutPreset) => {
    setActivePreset(preset.name);
    onUpdateLayout("maxWidth", preset.maxWidth);
    onUpdateLayout("borderRadius", preset.borderRadius);
    onUpdateLayout("spacing", preset.spacing);
  };

  const handleReset = () => {
    Object.entries(defaultLayout).forEach(([key, value]) => {
      onUpdateLayout(key as keyof Styles["layout"], value);
    });
    setActivePreset("Padrão");
  };

  const parseNumericValue = (value: string) => {
    return parseInt(value.replace("px", "")) || 0;
  };

  const formatSliderValue = (value: number, suffix: string = "px") => {
    return `${value}${suffix}`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Layout</CardTitle>
            <CardDescription>
              Configure a aparência geral da página de links
            </CardDescription>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  {showPreview ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{showPreview ? "Ocultar" : "Mostrar"} preview</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Presets */}
        <div>
          <Label className="mb-2 block">Presets Rápidos</Label>
          <div className="grid grid-cols-2 gap-2">
            {layoutPresets.map((preset) => (
              <Button
                key={preset.name}
                variant={activePreset === preset.name ? "default" : "outline"}
                size="lg"
                onClick={() => handlePresetChange(preset)}
                className="text-xs"
              >
                {preset.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Preview */}
        {showPreview && (
          <div className="border rounded-lg p-4 bg-muted/30">
            <Label className="mb-3 block text-sm font-medium">Preview</Label>
            <div className="flex items-center justify-center">
              <div
                className="bg-background border-2 border-dashed border-muted-foreground/30 transition-all duration-200"
                style={{
                  maxWidth: layout.maxWidth,
                  borderRadius: layout.borderRadius,
                  padding: layout.spacing,
                }}
              >
                <div className="space-y-2">
                  <div className="h-8 bg-muted rounded" />
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-10 bg-primary/20 rounded" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Max Width */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="maxWidth">Largura Máxima</Label>
            <span className="text-sm text-muted-foreground">
              {layout.maxWidth}
            </span>
          </div>
          <div className="space-y-2">
            <Slider
              id="maxWidth"
              min={320}
              max={800}
              step={40}
              value={[parseNumericValue(layout.maxWidth)]}
              onValueChange={([value]) =>
                onUpdateLayout("maxWidth", formatSliderValue(value))
              }
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>320px</span>
              <span>800px</span>
            </div>
          </div>
        </div>

        {/* Border Radius */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="borderRadius">Arredondamento</Label>
            <span className="text-sm text-muted-foreground">
              {layout.borderRadius}
            </span>
          </div>
          <div className="space-y-2">
            <Slider
              id="borderRadius"
              min={0}
              max={32}
              step={4}
              value={[parseNumericValue(layout.borderRadius)]}
              onValueChange={([value]) =>
                onUpdateLayout("borderRadius", formatSliderValue(value))
              }
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>0px</span>
              <span>32px</span>
            </div>
          </div>
          <div className="mt-2 flex gap-2">
            {[0, 4, 8, 12, 16, 24, 32].map((value) => (
              <Button
                key={value}
                variant="ghost"
                size="lg"
                className="h-6 w-6 p-0 text-xs"
                onClick={() =>
                  onUpdateLayout("borderRadius", formatSliderValue(value))
                }
              >
                {value}
              </Button>
            ))}
          </div>
        </div>

        {/* Spacing */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="spacing">Espaçamento</Label>
            <span className="text-sm text-muted-foreground">
              {layout.spacing}
            </span>
          </div>
          <div className="space-y-2">
            <Slider
              id="spacing"
              min={8}
              max={40}
              step={4}
              value={[parseNumericValue(layout.spacing)]}
              onValueChange={([value]) =>
                onUpdateLayout("spacing", formatSliderValue(value))
              }
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>8px</span>
              <span>40px</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={handleReset}
                  className="text-muted-foreground"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Resetar
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Restaurar configurações padrão</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardContent>
    </Card>
  );
};

export default LayoutSettings;
