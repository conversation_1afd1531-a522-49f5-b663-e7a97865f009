# Estrutura de Componentes - Refatoração da Página Home

Esta documentação descreve a refatoração realizada no arquivo `src/app/page.tsx`, quebrando-o em componentes menores e mais organizados.

## 📁 Estrutura de Arquivos

```
src/
├── app/
│   └── page.tsx                    # Arquivo principal simplificado
├── components/
│   └── home/                       # Componentes específicos da página home
│       ├── AdminPanel.tsx          # Painel de administração
│       ├── AdminToggle.tsx         # Botão para alternar painel admin
│       ├── ErrorNotification.tsx   # Componente de notificação de erro
│       ├── HomePageContainer.tsx   # Container principal da página
│       ├── LoadingSpinner.tsx      # Spinner de carregamento
│       ├── NoDataNotification.tsx  # Notificação de ausência de dados
│       └── index.ts               # Arquivo de índice para exportações
├── hooks/
│   └── useLinkData.ts             # Hook customizado para gerenciar dados
└── types/
    └── LinkPageData.ts            # Definições de tipos TypeScript
```

## 🧩 Componentes Criados

### 1. `HomePageContainer.tsx`

- **Propósito**: Container principal que orquestra toda a lógica da página home
- **Responsabilidades**:
  - Gerenciar estado do admin panel
  - Coordenar a busca de dados
  - Renderizar componentes filhos condicionalmente

### 2. `LoadingSpinner.tsx`

- **Propósito**: Exibir spinner de carregamento
- **Props**: Nenhuma
- **Uso**: Componente puro para indicar estado de loading

### 3. `ErrorNotification.tsx`

- **Propósito**: Exibir mensagens de erro com opção de retry
- **Props**:
  - `error: string` - Mensagem de erro
  - `onRetry: () => void` - Função para tentar novamente

### 4. `NoDataNotification.tsx`

- **Propósito**: Informar quando não há dados disponíveis
- **Props**: Nenhuma
- **Uso**: Componente puro para indicar ausência de dados

### 5. `AdminToggle.tsx`

- **Propósito**: Botão para alternar visibilidade do painel admin
- **Props**:
  - `showAdmin: boolean` - Estado atual do painel
  - `onToggle: () => void` - Função para alternar estado

### 6. `AdminPanel.tsx`

- **Propósito**: Painel overlay para configurações administrativas
- **Props**:
  - `showAdmin: boolean` - Controla visibilidade
  - `filename: string` - Nome do arquivo atual
  - `onFilenameChange: (filename: string) => void` - Callback para mudança de filename
  - `onLoadData: () => void` - Função para carregar dados

## 🎣 Hook Customizado

### `useLinkData.ts`

- **Propósito**: Gerenciar estado e lógica de busca de dados
- **Parâmetros**:
  - `filename: string` - Nome do arquivo a ser buscado
- **Retorna**:
  - `linkData: LinkPageData | null` - Dados carregados
  - `loading: boolean` - Estado de carregamento
  - `error: string | null` - Mensagem de erro, se houver
  - `refetch: () => void` - Função para recarregar dados

## 📝 Types

### `LinkPageData.ts`

- Interface TypeScript que define a estrutura dos dados da página
- Extraída do componente original para reutilização
- Contém definições para: userInfo, links, socialMedia, styles, seo

## 🏗️ Benefícios da Refatoração

### ✅ Vantagens:

1. **Separação de Responsabilidades**: Cada componente tem uma única responsabilidade
2. **Reutilização**: Componentes podem ser reutilizados em outras partes da aplicação
3. **Testabilidade**: Componentes menores são mais fáceis de testar
4. **Manutenibilidade**: Código organizado é mais fácil de manter e debugar
5. **Type Safety**: Tipos centralizados garantem consistência
6. **Hook Customizado**: Lógica de negócio extraída e reutilizável

### 📦 Estrutura Modular:

- **Pasta `home/`**: Agrupa componentes relacionados à página home
- **Arquivo `index.ts`**: Facilita importações com barrel exports
- **Hook dedicado**: Lógica de estado separada da apresentação
- **Types centralizados**: Definições de tipo reutilizáveis

## 🚀 Como Usar

### Importação Individual:

```tsx
import { LoadingSpinner, ErrorNotification } from "@/components/home";
```

### Importação do Container:

```tsx
import HomePageContainer from "@/components/home/<USER>";
```

### Usando o Hook:

```tsx
import { useLinkData } from "@/hooks/useLinkData";

const { linkData, loading, error, refetch } = useLinkData("links.json");
```

## 🔄 Migração

O arquivo `src/app/page.tsx` foi simplificado de ~190 linhas para apenas 7 linhas:

```tsx
"use client";

import React from "react";
import HomePageContainer from "@/components/home/<USER>";

export default function Home() {
  return <HomePageContainer />;
}
```

Esta refatoração mantém toda a funcionalidade original while improving code organization, reusability, and maintainability.
