# LinkHub - Crie sua Página de Links Personalizada

LinkHub é uma aplicação de código aberto que permite criar e personalizar sua própria página de links, similar a serviços como Linktree. Com uma interface de administração amigável, você pode gerenciar seus links, perfil, redes sociais e aparências, e depois exportar uma página estática pronta para ser hospedada em qualquer lugar.

## ✨ Funcionalidades

- **🎨 Editor Visual Intuitivo:** Uma interface de administração completa para personalizar cada aspecto da sua página sem precisar tocar em uma linha de código.
- **👤 Perfil Personalizável:** Adicione seu nome, biografia e uma foto de perfil.
- **🔗 Links Ilimitados:** Adicione, edite, reorganize e ative/desative quantos links desejar.
- **📱 Ícones de Redes Sociais:** Inclua links para suas redes sociais com ícones correspondentes.
- **🎨 Customização de Estilo:** Altere cores, fontes, bordas e até o plano de fundo (cores sólidas, gradientes ou imagens).
- **📝 Páginas de Privacidade e Termos:** Um editor para criar conteúdo para as páginas de "Política de Privacidade" and "Termos de Uso".
- **🚀 Exportação Estática:** Gere uma pasta `link-page` contendo arquivos HTML, CSS e JS puros, pronta para ser hospedada em qualquer serviço de hospedagem estática (Netlify, Vercel, GitHub Pages, etc.).
- **💾 Gerenciamento via JSON:** Todas as suas configurações são salvas em um arquivo JSON, facilitando o backup e a migração.

## 🚀 Como Usar

O fluxo de trabalho principal é dividido em duas partes: **edição** no painel de administração e **publicação** da página estática.

### 1. Editando sua Página

1.  **Inicie a Aplicação:**

    ```bash
    # Instale as dependências
    npm install

    # Inicie o servidor de desenvolvimento
    npm run dev
    ```

    Abra [http://localhost:3000/admin](http://localhost:3000/admin) para acessar o painel de administração.

2.  **Carregue ou Crie seus Dados:** Você pode carregar um arquivo JSON existente (como o `data/links.json`) ou começar do zero para criar sua página.

3.  **Personalize Tudo:** Use as abas no painel para:

    - **Informações:** Definir seu nome, bio, e configurações de SEO.
    - **Links:** Adicionar e gerenciar seus links.
    - **Social:** Configurar seus perfis de redes sociais.
    - **Estilos:** Mudar a aparência da sua página.
    - **Páginas:** Editar o conteúdo das páginas de privacidade e termos.

4.  **Salve seus Dados:** Clique no botão "Salvar" ou "Salvar e Exportar". Isso irá salvar suas configurações em um arquivo JSON dentro da pasta `/data` na raiz do projeto.

### 2. Publicando sua Página

Após salvar seus dados, você precisa preparar os arquivos para a hospedagem.

1.  **Copie a Pasta `link-page`:** Encontre a pasta `link-page` na raiz do projeto e faça uma cópia dela para um local de sua escolha. É **esta cópia** que você irá publicar.

2.  **Copie e Renomeie seu JSON:**

    - Pegue o arquivo JSON que você salvou (por exemplo, `data/meus-links.json`).
    - Copie este arquivo para dentro da pasta `data` da sua **cópia** da `link-page`.
    - **Renomeie** o arquivo para `links.json`.

    A estrutura final da sua pasta copiada deve ser:

    ```
    link-page/
    ├── css/
    ├── js/
    ├── assets/
    └── data/
        └── links.json  <-- Seu arquivo renomeado
    └── index.html
    ```

3.  **Hospede sua Pasta:** Faça o upload do conteúdo da sua pasta `link-page` copiada para o serviço de hospedagem de sua preferência. Sua página de links estará no ar!

Para um guia mais detalhado com passo a passo, acesse nosso [**Tutorial de Uso**](/tutorial.html).

## 📁 Estrutura do Projeto

```
.
├── data/              # Onde seus arquivos de dados JSON são salvos
├── link-page/         # Template da página estática que será hospedada
├── public/            # Arquivos públicos, incluindo o tutorial
└── src/
    └── app/
        └── admin/     # Onde fica a interface de administração
```

---

Construído com ❤️ para a comunidade de desenvolvedores.
