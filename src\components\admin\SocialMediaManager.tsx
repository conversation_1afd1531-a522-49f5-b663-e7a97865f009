import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SocialMediaItem } from "@/types/admin";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Plus, Trash2 } from "lucide-react";
import { socialPlatforms } from "@/constants/socialPlatforms";

interface SocialMediaManagerProps {
  socialMedia: SocialMediaItem[];
  onAddSocialMedia: () => void;
  onRemoveSocialMedia: (id: string) => void;
  onUpdateSocialMedia: (
    id: string,
    field: keyof SocialMediaItem,
    value: any
  ) => void;
}

const SocialMediaManager: React.FC<SocialMediaManagerProps> = ({
  socialMedia,
  onAddSocialMedia,
  onRemoveSocialMedia,
  onUpdateSocialMedia,
}) => {
  const handlePlatformSelect = (id: string, name: string) => {
    const { baseUrl } = socialPlatforms[name as keyof typeof socialPlatforms];
    onUpdateSocialMedia(id, "platform", name);
    onUpdateSocialMedia(id, "icon", name);
    
    // Get the current social media item
    const social = socialMedia.find(s => s.id === id);
    if (social && (!social.url || Object.values(socialPlatforms).some(p => p.baseUrl === social.url))) {
      onUpdateSocialMedia(id, "url", baseUrl);
    }
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Social Media
          <Button onClick={onAddSocialMedia} size="lg">
            <Plus className="w-4 h-4 mr-2" />
            Add Social
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {socialMedia.map((social) => (
          <div key={social.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between mb-3">
              <Switch
                checked={social.enabled}
                onCheckedChange={(checked) =>
                  onUpdateSocialMedia(social.id, "enabled", checked)
                }
              />
              <Button
                onClick={() => onRemoveSocialMedia(social.id)}
                variant="outline"
                size="lg"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            <div className="grid grid-cols-4 sm:grid-cols-6 gap-3 mb-3">
              {Object.entries(socialPlatforms).map(([name, { icon: Icon }]) => (
                <Button
                  key={name}
                  variant={social.platform === name ? "default" : "outline"}
                  size="icon"
                  className={`h-10 w-10 hover:scale-110 transition-transform ${social.platform === name ? "ring-2 ring-primary" : ""}`}
                  onClick={() => handlePlatformSelect(social.id, name)}
                  aria-label={name}
                >
                  <Icon className="w-5 h-5" />
                </Button>
              ))}
            </div>
            <div>
              <Label>URL</Label>
              <Input
                value={social.url}
                onChange={(e) =>
                  onUpdateSocialMedia(social.id, "url", e.target.value)
                }
                placeholder="https://instagram.com/username"
              />
            </div>
          </div>
        ))}
        {socialMedia.length === 0 && (
          <p className="text-center text-gray-500 py-8">
            No social media added yet. Click "Add Social" to get started.
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default SocialMediaManager;
