/* Base styles for the background element */
.bg {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
}

/* Background Style: Gradient Top */
.bg.bg-gradient-top {
    background: radial-gradient(125% 125% at 50% 90%, #fff 40%, #6366f1 100%);
}

/* Background Style: Dots */
.bg.bg-dots {
    background: #ffffff;
    background-image: radial-gradient(circle at 1px 1px, rgba(0, 0, 0, 0.35) 1px, transparent 0);
    background-size: 20px 20px;
}

/* Background Style: Dreamy */
.bg.bg-dreamy {
    background-color: #fefcff;
    background-image: radial-gradient(circle at 30% 70%, rgba(173, 216, 230, 0.35), transparent 60%),
        radial-gradient(circle at 70% 30%, rgba(255, 182, 193, 0.4), transparent 60%);
}

/* Background Style: Grid */
.bg.bg-grid {
    background: white;
    background-image: linear-gradient(to right, rgba(71, 85, 105, 0.15) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(71, 85, 105, 0.15) 1px, transparent 1px),
        radial-gradient(circle at 50% 60%, rgba(236, 72, 153, 0.15) 0%, rgba(168, 85, 247, 0.05) 40%, transparent 70%);
    background-size: 40px 40px, 40px 40px, 100% 100%;
}

/* Background Style: Gradient Bottom */
.bg.bg-gradient-bottom {
    background: radial-gradient(125% 125% at 50% 10%, #fff 40%, #6366f1 100%);
}

/* Background Style: Waves */
.bg.bg-waves {
    background-color: #f0f8ff;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 80' width='80' height='80'%3E%3Cpath fill='%23d4e4ff' fill-opacity='0.4' d='M0 0h80v80H0zM20 20h40v40H20z'/%3E%3Cpath fill='%23c7d2fe' fill-opacity='0.4' d='M0 0h20v20H0zM60 0h20v20H60zM0 60h20v20H0zM60 60h20v20H60z'/%3E%3C/svg%3E");
}

/* Background Style: Bubbles */
.bg.bg-bubbles {
    background: #e0e7ff;
    background-image: radial-gradient(at 20% 80%, #a5b4fc 0px, transparent 50%),
                      radial-gradient(at 80% 20%, #a5b4fc 0px, transparent 50%),
                      radial-gradient(at 50% 50%, #c7d2fe 0px, transparent 50%);
    background-size: 100px 100px;
}