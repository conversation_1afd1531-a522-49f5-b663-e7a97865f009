import React from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Settings2 } from "lucide-react";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Styles } from "@/types/admin";

interface LayoutQuickPanelProps {
  layout: Styles["layout"];
  onUpdateLayout: (field: keyof Styles["layout"], value: string) => void;
}

const LayoutQuickPanel: React.FC<LayoutQuickPanelProps> = ({
  layout,
  onUpdateLayout,
}) => {
  const parseNumericValue = (value: string) => {
    return parseInt(value.replace("px", "")) || 0;
  };

  const formatSliderValue = (value: number, suffix: string = "px") => {
    return `${value}${suffix}`;
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="lg"
          className="fixed bottom-4 right-4 z-50 shadow-lg"
        >
          <Settings2 className="h-4 w-4 mr-2" />
          Layout Rápido
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium">Ajustes Rápidos</h4>
            <p className="text-sm text-muted-foreground">
              Faça ajustes visuais instantâneos
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm">Largura</Label>
                <span className="text-xs text-muted-foreground">
                  {layout.maxWidth}
                </span>
              </div>
              <Slider
                min={320}
                max={800}
                step={40}
                value={[parseNumericValue(layout.maxWidth)]}
                onValueChange={([value]) =>
                  onUpdateLayout("maxWidth", formatSliderValue(value))
                }
              />
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm">Arredondamento</Label>
                <span className="text-xs text-muted-foreground">
                  {layout.borderRadius}
                </span>
              </div>
              <Slider
                min={0}
                max={32}
                step={4}
                value={[parseNumericValue(layout.borderRadius)]}
                onValueChange={([value]) =>
                  onUpdateLayout("borderRadius", formatSliderValue(value))
                }
              />
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm">Espaçamento</Label>
                <span className="text-xs text-muted-foreground">
                  {layout.spacing}
                </span>
              </div>
              <Slider
                min={8}
                max={40}
                step={4}
                value={[parseNumericValue(layout.spacing)]}
                onValueChange={([value]) =>
                  onUpdateLayout("spacing", formatSliderValue(value))
                }
              />
            </div>
          </div>

          <div className="pt-2 border-t">
            <div className="text-xs text-muted-foreground">
              Use os controles acima para ajustar visualização em tempo real
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default LayoutQuickPanel;
